import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AuthService } from '../../services/auth.service';
import { RegisterRequest } from '../../models/auth.model';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterLink,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule
  ],
  template: `
    <div class="register-container">
      <mat-card class="register-card">
        <mat-card-header>
          <mat-card-title>{{isRegistered ? 'Đăng ký thành công!' : 'Đăng ký tài khoản'}}</mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <!-- Success Message -->
          <div class="success-content" *ngIf="isRegistered">
            <div class="icon-container success">
              <mat-icon>check_circle</mat-icon>
            </div>
            <h3>Kiểm tra email của bạn!</h3>
            <p>
              Chúng tôi đã gửi một email xác nhận đến <strong>{{userData.email}}</strong>. 
              Vui lòng kiểm tra hộp thư và nhấp vào liên kết để kích hoạt tài khoản.
            </p>
            <div class="actions">
              <button mat-raised-button color="primary" routerLink="/login">
                <mat-icon>login</mat-icon>
                Đăng nhập
              </button>
              <button mat-button (click)="resendVerification()" [disabled]="isResending">
                <mat-icon>refresh</mat-icon>
                {{isResending ? 'Đang gửi...' : 'Gửi lại email'}}
              </button>
            </div>
          </div>

          <!-- Registration Form -->
          <form #registerForm="ngForm" (ngSubmit)="onSubmit()" *ngIf="!isRegistered">
            <div class="row">
              <mat-form-field appearance="outline" class="half-width">
                <mat-label>Họ</mat-label>
                <input 
                  matInput 
                  [(ngModel)]="userData.firstName" 
                  name="firstName"
                  required
                  minlength="2"
                  maxlength="50"
                  #firstName="ngModel"
                  placeholder="Nguyễn">
                <mat-icon matSuffix>person</mat-icon>
                <mat-error *ngIf="firstName.invalid && firstName.touched">
                  <span *ngIf="firstName.errors?.['required']">Họ là bắt buộc</span>
                  <span *ngIf="firstName.errors?.['minlength']">Họ phải có từ 2-50 ký tự</span>
                  <span *ngIf="firstName.errors?.['maxlength']">Họ phải có từ 2-50 ký tự</span>
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="half-width">
                <mat-label>Tên</mat-label>
                <input 
                  matInput 
                  [(ngModel)]="userData.lastName" 
                  name="lastName"
                  required
                  minlength="2"
                  maxlength="50"
                  #lastName="ngModel"
                  placeholder="Văn A">
                <mat-error *ngIf="lastName.invalid && lastName.touched">
                  <span *ngIf="lastName.errors?.['required']">Tên là bắt buộc</span>
                  <span *ngIf="lastName.errors?.['minlength']">Tên phải có từ 2-50 ký tự</span>
                  <span *ngIf="lastName.errors?.['maxlength']">Tên phải có từ 2-50 ký tự</span>
                </mat-error>
              </mat-form-field>
            </div>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Tên đăng nhập</mat-label>
              <input 
                matInput 
                [(ngModel)]="userData.username" 
                name="username"
                required
                minlength="3"
                maxlength="30"
                pattern="^[a-zA-Z0-9_]+$"
                #username="ngModel"
                placeholder="username">
              <mat-icon matSuffix>account_circle</mat-icon>
              <mat-error *ngIf="username.invalid && username.touched">
                <span *ngIf="username.errors?.['required']">Tên đăng nhập là bắt buộc</span>
                <span *ngIf="username.errors?.['minlength']">Tên đăng nhập phải có từ 3-30 ký tự</span>
                <span *ngIf="username.errors?.['maxlength']">Tên đăng nhập phải có từ 3-30 ký tự</span>
                <span *ngIf="username.errors?.['pattern']">Tên đăng nhập chỉ được chứa chữ cái, số và dấu gạch dưới</span>
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Email</mat-label>
              <input 
                matInput 
                type="email" 
                [(ngModel)]="userData.email" 
                name="email"
                required
                email
                #email="ngModel"
                placeholder="<EMAIL>">
              <mat-icon matSuffix>email</mat-icon>
              <mat-error *ngIf="email.invalid && email.touched">
                <span *ngIf="email.errors?.['required']">Email là bắt buộc</span>
                <span *ngIf="email.errors?.['email']">Email không hợp lệ</span>
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Mật khẩu</mat-label>
              <input 
                matInput 
                [type]="showPassword ? 'text' : 'password'"
                [(ngModel)]="userData.password" 
                name="password"
                required
                minlength="6"
                #password="ngModel"
                placeholder="Ít nhất 6 ký tự với chữ hoa, chữ thường, số và ký tự đặc biệt">
              <button 
                mat-icon-button 
                matSuffix 
                type="button"
                (click)="showPassword = !showPassword">
                <mat-icon>{{showPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
              </button>
              <mat-error *ngIf="password.invalid && password.touched">
                <span *ngIf="password.errors?.['required']">Mật khẩu là bắt buộc</span>
                <span *ngIf="password.errors?.['minlength']">Mật khẩu phải có ít nhất 6 ký tự</span>
              </mat-error>
              <mat-error *ngIf="password.valid && password.touched && !isPasswordStrong(userData.password)">
                Mật khẩu phải chứa ít nhất 1 chữ thường, 1 chữ hoa, 1 số và 1 ký tự đặc biệt
              </mat-error>
            </mat-form-field>

            <!-- Password Requirements -->
            <div class="password-requirements" *ngIf="userData.password && !isPasswordStrong(userData.password)">
              <p class="requirement-title">Yêu cầu mật khẩu:</p>
              <ul class="requirements-list">
                <li [class.valid]="hasLowercase(userData.password)">
                  <mat-icon>{{hasLowercase(userData.password) ? 'check' : 'close'}}</mat-icon>
                  Ít nhất 1 chữ thường (a-z)
                </li>
                <li [class.valid]="hasUppercase(userData.password)">
                  <mat-icon>{{hasUppercase(userData.password) ? 'check' : 'close'}}</mat-icon>
                  Ít nhất 1 chữ hoa (A-Z)
                </li>
                <li [class.valid]="hasNumber(userData.password)">
                  <mat-icon>{{hasNumber(userData.password) ? 'check' : 'close'}}</mat-icon>
                  Ít nhất 1 số (0-9)
                </li>
                <li [class.valid]="hasSpecialChar(userData.password)">
                  <mat-icon>{{hasSpecialChar(userData.password) ? 'check' : 'close'}}</mat-icon>
                  Ít nhất 1 ký tự đặc biệt (!#$%^&*)
                </li>
                <li [class.valid]="userData.password.length >= 6">
                  <mat-icon>{{userData.password.length >= 6 ? 'check' : 'close'}}</mat-icon>
                  Tối thiểu 6 ký tự
                </li>
              </ul>
            </div>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Xác nhận mật khẩu</mat-label>
              <input 
                matInput 
                [type]="showConfirmPassword ? 'text' : 'password'"
                [(ngModel)]="userData.confirmPassword" 
                name="confirmPassword"
                required
                #confirmPassword="ngModel"
                placeholder="Nhập lại mật khẩu">
              <button 
                mat-icon-button 
                matSuffix 
                type="button"
                (click)="showConfirmPassword = !showConfirmPassword">
                <mat-icon>{{showConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
              </button>
              <mat-error *ngIf="confirmPassword.touched && userData.password !== userData.confirmPassword">
                Mật khẩu xác nhận không khớp
              </mat-error>
            </mat-form-field>

            <button 
              mat-raised-button 
              color="primary" 
              type="submit"
              class="full-width"
              [disabled]="!isFormValid() || isLoading">
              {{isLoading ? 'Đang đăng ký...' : 'Đăng ký'}}
            </button>
          </form>

          <div class="login-link" *ngIf="!isRegistered">
            <p>Đã có tài khoản? <a routerLink="/login">Đăng nhập</a></p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .register-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background-color: #f5f5f5;
      padding: 20px;
    }

    .register-card {
      width: 100%;
      max-width: 500px;
      padding: 20px;
    }

    mat-card-header {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;
    }

    mat-card-title {
      font-size: 24px;
      font-weight: 500;
    }

    .success-content {
      text-align: center;
      padding: 20px 0;
    }

    .icon-container {
      margin: 20px 0;
    }

    .icon-container mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
    }

    .icon-container.success mat-icon {
      color: #4caf50;
    }

    .success-content h3 {
      margin: 20px 0 10px 0;
      color: #333;
    }

    .success-content p {
      color: #666;
      margin-bottom: 30px;
      line-height: 1.5;
    }

    .actions {
      display: flex;
      flex-direction: column;
      gap: 12px;
      align-items: center;
    }

    .actions button {
      min-width: 200px;
    }

    .row {
      display: flex;
      gap: 16px;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .half-width {
      flex: 1;
      margin-bottom: 16px;
    }

    .login-link {
      text-align: center;
      margin-top: 20px;
    }

    .login-link a {
      color: #3f51b5;
      text-decoration: none;
      font-weight: 500;
    }

    .login-link a:hover {
      text-decoration: underline;
    }

    mat-error {
      font-size: 12px;
    }

    .password-requirements {
      margin-bottom: 16px;
      padding: 12px;
      background-color: #f8f9fa;
      border-radius: 4px;
      border-left: 4px solid #2196f3;
    }

    .requirement-title {
      margin: 0 0 8px 0;
      font-weight: 500;
      color: #333;
      font-size: 14px;
    }

    .requirements-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .requirements-list li {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 2px 0;
      font-size: 13px;
      color: #666;
    }

    .requirements-list li.valid {
      color: #4caf50;
    }

    .requirements-list li mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .requirements-list li.valid mat-icon {
      color: #4caf50;
    }

    .requirements-list li:not(.valid) mat-icon {
      color: #f44336;
    }
  `]
})
export class RegisterComponent {
  userData: RegisterRequest = {
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    username: ''
  };
  showPassword = false;
  showConfirmPassword = false;
  isLoading = false;
  isRegistered = false;
  isResending = false;

  constructor(
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  onSubmit(): void {
    if (this.isFormValid()) {
      this.isLoading = true;
      
      this.authService.register(this.userData).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.isRegistered = true;
          this.snackBar.open(response.message, 'Đóng', { duration: 5000 });
        },
        error: (error) => {
          this.isLoading = false;
          const message = error.error?.message || 'Đăng ký thất bại';
          this.snackBar.open(message, 'Đóng', { duration: 3000 });
        }
      });
    }
  }

  resendVerification(): void {
    if (!this.userData.email) {
      this.snackBar.open('Email không hợp lệ', 'Đóng', { duration: 3000 });
      return;
    }

    this.isResending = true;
    this.authService.resendVerification({ email: this.userData.email }).subscribe({
      next: (response) => {
        this.isResending = false;
        this.snackBar.open(response.message, 'Đóng', { duration: 5000 });
      },
      error: (error) => {
        this.isResending = false;
        const message = error.error?.message || 'Không thể gửi lại email xác nhận';
        this.snackBar.open(message, 'Đóng', { duration: 3000 });
      }
    });
  }

  isFormValid(): boolean {
    return !!(
      this.userData.email &&
      this.userData.password &&
      this.userData.confirmPassword &&
      this.userData.firstName &&
      this.userData.lastName &&
      this.userData.username &&
      this.userData.password === this.userData.confirmPassword &&
      this.isPasswordStrong(this.userData.password) &&
      this.userData.firstName.length >= 2 &&
      this.userData.lastName.length >= 2 &&
      this.userData.username.length >= 3 &&
      this.isUsernameValid(this.userData.username)
    );
  }

  isPasswordStrong(password: string): boolean {
    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{6,}$/;
    return regex.test(password);
  }

  hasLowercase(password: string): boolean {
    return /[a-z]/.test(password);
  }

  hasUppercase(password: string): boolean {
    return /[A-Z]/.test(password);
  }

  hasNumber(password: string): boolean {
    return /\d/.test(password);
  }

  hasSpecialChar(password: string): boolean {
    return /[^\da-zA-Z]/.test(password);
  }

  isUsernameValid(username: string): boolean {
    const regex = /^[a-zA-Z0-9_]+$/;
    return regex.test(username);
  }
} 