using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using LibraryManagement.Core.Interfaces;
using LibraryManagement.Core.Entities;
using LibraryManagement.Application.DTOs;
using LibraryManagement.Core.Enums;
using System.Linq;

namespace LibraryManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[AllowAnonymous] // Allow anonymous access for all endpoints
public class MembersController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;

    public MembersController(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<MemberDto>>> GetMembers()
    {
        var members = await _unitOfWork.Members.GetAllAsync();

        var memberDtos = members.Select(member => new MemberDto
        {
            Id = member.Id,
            FirstName = member.FirstName,
            LastName = member.LastName,
            FullName = member.FullName,
            Email = member.Email,
            Phone = member.Phone,
            Address = member.Address,
            MembershipDate = member.MembershipDate,
            Status = member.Status,
            StatusName = GetStatusName(member.Status),
            Notes = member.Notes,
            CreatedAt = member.CreatedAt,
            UpdatedAt = member.UpdatedAt
        }).ToList();

        return Ok(memberDtos);
    }

    [HttpGet("{id}")]
    [Authorize(Policy = "MemberManagement")]
    public async Task<ActionResult<MemberDto>> GetMember(int id)
    {
        var member = await _unitOfWork.Members.GetByIdAsync(id);
        if (member == null)
        {
            return NotFound();
        }

        var memberDto = new MemberDto
        {
            Id = member.Id,
            FirstName = member.FirstName,
            LastName = member.LastName,
            FullName = member.FullName,
            Email = member.Email,
            Phone = member.Phone,
            Address = member.Address,
            MembershipDate = member.MembershipDate,
            Status = member.Status,
            StatusName = GetStatusName(member.Status),
            Notes = member.Notes,
            CreatedAt = member.CreatedAt,
            UpdatedAt = member.UpdatedAt
        };

        return Ok(memberDto);
    }

    [HttpPost]
    [Authorize(Policy = "MemberManagement")]
    public async Task<ActionResult<MemberDto>> CreateMember(CreateMemberDto createMemberDto)
    {
        // Check if email already exists
        var existingMember = await _unitOfWork.Members.FindAsync(m => m.Email == createMemberDto.Email || m.Phone == createMemberDto.Phone);
        if (existingMember.Any())
        {
            var emailConflict = existingMember.Any(m => m.Email == createMemberDto.Email);
            var phoneConflict = existingMember.Any(m => m.Phone == createMemberDto.Phone);

            if (emailConflict && phoneConflict)
            {
                return BadRequest(new { message = "Email và số điện thoại đã được sử dụng bởi thành viên khác." });
            }
            if (emailConflict)
            {
                return BadRequest(new { message = "Email đã được sử dụng bởi thành viên khác." });
            }
            if (phoneConflict)
            {
                return BadRequest(new { message = "Số điện thoại đã được sử dụng bởi thành viên khác." });
            }
        }

        var member = new Member
        {
            FirstName = createMemberDto.FirstName,
            LastName = createMemberDto.LastName,
            Email = createMemberDto.Email,
            Phone = createMemberDto.Phone,
            Address = createMemberDto.Address,
            MembershipDate = DateTime.UtcNow,
            Status = MemberStatus.Active,
            Notes = createMemberDto.Notes
        };

        await _unitOfWork.Members.AddAsync(member);
        await _unitOfWork.SaveChangesAsync();

        var memberDto = new MemberDto
        {
            Id = member.Id,
            FirstName = member.FirstName,
            LastName = member.LastName,
            FullName = member.FullName,
            Email = member.Email,
            Phone = member.Phone,
            Address = member.Address,
            MembershipDate = member.MembershipDate,
            Status = member.Status,
            StatusName = GetStatusName(member.Status),
            Notes = member.Notes,
            CreatedAt = member.CreatedAt,
            UpdatedAt = member.UpdatedAt
        };

        return CreatedAtAction(nameof(GetMember), new { id = member.Id }, memberDto);
    }

    [HttpPut("{id}")]
    [Authorize(Policy = "MemberManagement")]
    public async Task<IActionResult> UpdateMember(int id, UpdateMemberDto updateMemberDto)
    {
        var member = await _unitOfWork.Members.GetByIdAsync(id);
        if (member == null)
        {
            return NotFound();
        }

        // Check if email is being changed and already exists for another member
        var conflictMembers = await _unitOfWork.Members.FindAsync(m => (m.Email == updateMemberDto.Email || m.Phone == updateMemberDto.Phone) && m.Id != id);
        if (conflictMembers.Any())
        {
            var emailConflict = conflictMembers.Any(m => m.Email == updateMemberDto.Email);
            var phoneConflict = conflictMembers.Any(m => m.Phone == updateMemberDto.Phone);

            if (emailConflict && phoneConflict)
            {
                return BadRequest(new { messgae = "Email và số điện thoại đã được sử dụng bởi thành viên khác." });
            }
            if (emailConflict)
            {
                return BadRequest(new { messgae = "Email đã được sử dụng bởi thành viên khác." });
            }
            if (phoneConflict)
            {
                return BadRequest(new { message = "Số điện thoại đã được sử dụng bởi thành viên khác." });
            }
        }

        member.FirstName = updateMemberDto.FirstName;
        member.LastName = updateMemberDto.LastName;
        member.Email = updateMemberDto.Email;
        member.Phone = updateMemberDto.Phone;
        member.Address = updateMemberDto.Address;
        member.Status = updateMemberDto.Status;
        member.Notes = updateMemberDto.Notes;

        await _unitOfWork.Members.UpdateAsync(member);
        await _unitOfWork.SaveChangesAsync();

        return NoContent();
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = "MemberManagement")]
    public async Task<IActionResult> DeleteMember(int id)
    {
        try
        {
            var member = await _unitOfWork.Members.GetByIdAsync(id);
            if (member == null)
            {
                return NotFound();
            }

            // Check if member has any borrow records (active or historical)
            var borrowRecords = (await _unitOfWork.BorrowRecords.FindAsync(br => br.MemberId == id)).ToList();

            if (borrowRecords.Any())
            {
                var activeBorrows = borrowRecords.Where(br => br.ReturnDate == null).ToList();
                var historicalBorrows = borrowRecords.Where(br => br.ReturnDate != null).ToList();

                if (activeBorrows.Any())
                {
                    return BadRequest("Không thể xóa thành viên đang có sách mượn chưa trả. Vui lòng đợi thành viên trả hết sách trước khi xóa.");
                }
                else if (historicalBorrows.Any())
                {
                    return BadRequest($"Không thể xóa thành viên có lịch sử mượn sách ({historicalBorrows.Count} lượt mượn). Để bảo toàn dữ liệu lịch sử, vui lòng chuyển thành viên sang trạng thái 'Tạm ngưng' hoặc 'Hết hạn' thay vì xóa.");
                }
            }

            await _unitOfWork.Members.DeleteAsync(member);
            await _unitOfWork.SaveChangesAsync();

            return NoContent();
        }
        catch (Exception ex)
        {
            // Log the exception details for debugging
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpGet("search")]
    [Authorize(Policy = "MemberManagement")]
    public async Task<ActionResult<IEnumerable<MemberDto>>> SearchMembers(
        [FromQuery] string? firstName,
        [FromQuery] string? lastName,
        [FromQuery] string? email,
        [FromQuery] MemberStatus? status)
    {
        var members = await _unitOfWork.Members.GetAllAsync();

        var filteredMembers = members.AsEnumerable();

        if (!string.IsNullOrEmpty(firstName))
        {
            filteredMembers = filteredMembers.Where(m =>
                m.FirstName.Contains(firstName, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(lastName))
        {
            filteredMembers = filteredMembers.Where(m =>
                m.LastName.Contains(lastName, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(email))
        {
            filteredMembers = filteredMembers.Where(m =>
                m.Email.Contains(email, StringComparison.OrdinalIgnoreCase));
        }

        if (status.HasValue)
        {
            filteredMembers = filteredMembers.Where(m => m.Status == status.Value);
        }

        var memberDtos = filteredMembers.Select(member => new MemberDto
        {
            Id = member.Id,
            FirstName = member.FirstName,
            LastName = member.LastName,
            FullName = member.FullName,
            Email = member.Email,
            Phone = member.Phone,
            Address = member.Address,
            MembershipDate = member.MembershipDate,
            Status = member.Status,
            StatusName = GetStatusName(member.Status),
            Notes = member.Notes,
            CreatedAt = member.CreatedAt,
            UpdatedAt = member.UpdatedAt
        }).ToList();

        return Ok(memberDtos);
    }

    [HttpGet("{id}/borrow-history")]
    [Authorize(Policy = "BorrowOperations")]
    public async Task<ActionResult<IEnumerable<BorrowRecordDto>>> GetMemberBorrowHistory(int id)
    {
        var member = await _unitOfWork.Members.GetByIdAsync(id);
        if (member == null)
        {
            return NotFound();
        }

        var borrowRecords = (await _unitOfWork.BorrowRecords.FindAsync(br => br.MemberId == id)).ToList();
        var bookIds = borrowRecords.Select(br => br.BookId).Distinct().ToList();
        var books = (await _unitOfWork.Books.FindAsync(b => bookIds.Contains(b.Id))).ToDictionary(b => b.Id);

        var borrowRecordDtos = borrowRecords.Select(br => new BorrowRecordDto
        {
            Id = br.Id,
            BookId = br.BookId,
            BookTitle = books.GetValueOrDefault(br.BookId)?.Title ?? "Unknown",
            BookAuthor = books.GetValueOrDefault(br.BookId)?.Author ?? "Unknown",
            MemberId = br.MemberId,
            MemberName = member.FullName,
            BorrowDate = br.BorrowDate,
            DueDate = br.DueDate,
            ReturnDate = br.ReturnDate,
            Status = br.Status,
            StatusName = GetStatusName(br.Status),
            Notes = br.Notes,
            Fine = br.Fine,
            IsOverdue = br.IsOverdue,
            DaysOverdue = br.DaysOverdue,
            CreatedAt = br.CreatedAt
        }).OrderByDescending(br => br.BorrowDate).ToList();

        return Ok(borrowRecordDtos);
    }

    private static string GetStatusName(MemberStatus status)
    {
        return status switch
        {
            MemberStatus.Active => "Hoạt động",
            MemberStatus.Suspended => "Tạm ngưng",
            MemberStatus.Expired => "Hết hạn",
            MemberStatus.Banned => "Bị cấm",
            _ => "Không xác định"
        };
    }

    private string GetStatusName(BorrowStatus status)
    {
        return status switch
        {
            BorrowStatus.Borrowed => "Đang mượn",
            BorrowStatus.Returned => "Đã trả",
            BorrowStatus.Overdue => "Quá hạn",
            BorrowStatus.Lost => "Mất sách",
            BorrowStatus.Renewed => "Gia hạn",
            _ => "Không xác định"
        };
    }
}