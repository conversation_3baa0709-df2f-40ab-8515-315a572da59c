using CloudinaryDotNet;
using CloudinaryDotNet.Actions;
using LibraryManagement.Core.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Linq;

namespace LibraryManagement.Infrastructure.Services;

public class CloudinaryService : ICloudinaryService
{
    private readonly Cloudinary _cloudinary;
    private readonly ILogger<CloudinaryService> _logger;
    private readonly string _uploadFolder;

    public CloudinaryService(IConfiguration configuration, ILogger<CloudinaryService> logger)
    {
        _logger = logger;
        
        var cloudName = configuration["Cloudinary:CloudName"];
        var apiKey = configuration["Cloudinary:ApiKey"];
        var apiSecret = configuration["Cloudinary:ApiSecret"];
        _uploadFolder = configuration["Cloudinary:UploadFolder"] ?? "library-management";

        if (string.IsNullOrEmpty(cloudName) || string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(apiSecret))
        {
            throw new InvalidOperationException("Cloudinary configuration is missing. Please check your appsettings.json");
        }

        var account = new Account(cloudName, apiKey, apiSecret);
        _cloudinary = new Cloudinary(account);
    }

    public async Task<string> UploadImageAsync(Stream imageStream, string fileName)
    {
        try
        {
            var uploadParams = new ImageUploadParams()
            {
                File = new FileDescription(fileName, imageStream),
                Folder = _uploadFolder,
                UseFilename = false,
                UniqueFilename = true,
                Overwrite = false,
                Transformation = new Transformation()
                    .Width(800)
                    .Height(600)
                    .Crop("limit")
                    .Quality("auto:good")
                    .FetchFormat("auto")
            };

            var uploadResult = await _cloudinary.UploadAsync(uploadParams);

            if (uploadResult.Error != null)
            {
                _logger.LogError("Cloudinary upload error: {Error}", uploadResult.Error.Message);
                throw new Exception($"Cloudinary upload failed: {uploadResult.Error.Message}");
            }

            _logger.LogInformation("Image uploaded successfully: {PublicId}", uploadResult.PublicId);
            return uploadResult.SecureUrl.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading image to Cloudinary");
            throw;
        }
    }

    public async Task<bool> DeleteImageAsync(string publicId)
    {
        try
        {
            var deleteParams = new DeletionParams(publicId)
            {
                ResourceType = ResourceType.Image
            };

            var result = await _cloudinary.DestroyAsync(deleteParams);
            
            if (result.Error != null)
            {
                _logger.LogError("Cloudinary delete error: {Error}", result.Error.Message);
                return false;
            }

            _logger.LogInformation("Image deleted successfully: {PublicId}", publicId);
            return result.Result == "ok";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting image from Cloudinary");
            return false;
        }
    }

    public string GetPublicIdFromUrl(string imageUrl)
    {
        if (string.IsNullOrEmpty(imageUrl))
            return string.Empty;

        try
        {
            // Extract public ID from Cloudinary URL
            // Example: https://res.cloudinary.com/cloud-name/image/upload/v1234567890/folder/filename.jpg
            var uri = new Uri(imageUrl);
            var pathSegments = uri.AbsolutePath.Split('/');
            
            // Find the upload segment and get everything after it
            var uploadIndex = Array.IndexOf(pathSegments, "upload");
            if (uploadIndex != -1 && uploadIndex < pathSegments.Length - 2)
            {
                // Skip version if present (starts with 'v' followed by numbers)
                var startIndex = uploadIndex + 1;
                if (pathSegments[startIndex].StartsWith('v') && pathSegments[startIndex].Length > 1 && 
                    pathSegments[startIndex].Substring(1).All(char.IsDigit))
                {
                    startIndex++;
                }

                // Combine remaining segments and remove file extension
                var publicIdWithExtension = string.Join("/", pathSegments.Skip(startIndex));
                var lastDotIndex = publicIdWithExtension.LastIndexOf('.');
                
                return lastDotIndex > 0 ? publicIdWithExtension.Substring(0, lastDotIndex) : publicIdWithExtension;
            }

            return string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting public ID from URL: {Url}", imageUrl);
            return string.Empty;
        }
    }
} 