import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { MatTableModule } from "@angular/material/table";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatCardModule } from "@angular/material/card";
import { MatInputModule } from "@angular/material/input";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatSnackBar, MatSnackBarModule } from "@angular/material/snack-bar";
import { MatDialogModule, MatDialog } from "@angular/material/dialog";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatMenuModule } from "@angular/material/menu";
import { MatTooltipModule } from "@angular/material/tooltip";
import { MatDividerModule } from "@angular/material/divider";
import { MatChipsModule } from "@angular/material/chips";
import { MatBadgeModule } from "@angular/material/badge";
import { FormsModule } from "@angular/forms";
import { SelectionModel } from "@angular/cdk/collections";
import { Book } from "../../models/book.model";
import { BookService } from "../../services/book.service";
import { AuthService } from "../../services/auth.service";
import { ThemeService } from "../../services/theme.service";
import { LoadingStateComponent } from "../../shared/loading-state/loading-state.component";
import { EmptyStateComponent } from "../../shared/empty-state/empty-state.component";

@Component({
  selector: "app-book-list",
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatInputModule,
    MatFormFieldModule,
    MatSnackBarModule,
    MatDialogModule,
    MatCheckboxModule,
    MatMenuModule,
    MatTooltipModule,
    MatDividerModule,
    MatChipsModule,
    MatBadgeModule,
    FormsModule,
    LoadingStateComponent,
    EmptyStateComponent,
  ],
  template: `
    <div class="book-list-container">
      <!-- Header Section -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <mat-icon class="title-icon">menu_book</mat-icon>
            Quản lý Sách
          </h1>
          <p class="page-subtitle">Danh sách và quản lý tất cả sách trong thư viện</p>
        </div>
        <div class="header-actions">
          <button
            type="button"
            mat-raised-button
            color="primary"
            routerLink="/books/add"
            *ngIf="canManageBooks()"
            class="add-button">
            <mat-icon>add</mat-icon>
            Thêm sách mới
          </button>
        </div>
      </div>

      <!-- Search and Filter Section -->
      <mat-card class="search-card">
        <mat-card-content>
          <div class="search-section">
            <mat-form-field appearance="outline" class="search-field">
              <mat-label>Tìm kiếm sách...</mat-label>
              <input
                matInput
                [(ngModel)]="searchTerm"
                (keyup.enter)="onSearch()"
                placeholder="Nhập tên sách, tác giả, ISBN...">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
            <div class="search-actions">
              <button type="button" mat-raised-button color="primary" (click)="onSearch()">
                <mat-icon>search</mat-icon>
                Tìm kiếm
              </button>
              <button type="button" mat-stroked-button (click)="clearSearch()" *ngIf="searchTerm">
                <mat-icon>clear</mat-icon>
                Xóa
              </button>
            </div>
          </div>

          <!-- Bulk Actions -->
          <div class="bulk-actions" *ngIf="selection.hasValue() && canManageBooks()">
            <div class="selection-info">
              <mat-icon>check_circle</mat-icon>
              <span class="selected-count">
                Đã chọn {{ selection.selected.length }} sách
              </span>
            </div>
            <div class="bulk-actions-buttons">
              <button
                type="button"
                mat-raised-button
                color="warn"
                (click)="deleteSelectedBooks()"
                [disabled]="isDeleting"
                matTooltip="Xóa các sách đã chọn">
                <mat-icon>delete</mat-icon>
                Xóa đã chọn
              </button>
              <button
                type="button"
                mat-stroked-button
                (click)="selection.clear()"
                matTooltip="Bỏ chọn tất cả">
                <mat-icon>clear</mat-icon>
                Bỏ chọn
              </button>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Loading State -->
      <app-loading-state
        *ngIf="isLoading"
        message="Đang tải danh sách sách..."
        subMessage="Vui lòng đợi trong giây lát">
      </app-loading-state>

      <!-- Empty State -->
      <app-empty-state
        *ngIf="!isLoading && books.length === 0 && !searchTerm"
        icon="menu_book"
        title="Chưa có sách nào"
        message="Thư viện chưa có sách nào. Hãy thêm sách đầu tiên để bắt đầu."
        actionText="Thêm sách mới"
        actionIcon="add"
        (action)="navigateToAdd()"
        [actionDisabled]="!canManageBooks()">
      </app-empty-state>

      <!-- No Search Results -->
      <app-empty-state
        *ngIf="!isLoading && books.length === 0 && searchTerm"
        icon="search_off"
        title="Không tìm thấy kết quả"
        [message]="'Không tìm thấy sách nào với từ khóa: ' + searchTerm"
        actionText="Xóa tìm kiếm"
        actionIcon="clear"
        (action)="clearSearch()">
      </app-empty-state>

      <!-- Books Table -->
      <mat-card *ngIf="!isLoading && books.length > 0" class="table-card">
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="books" class="books-table">
              <!-- Checkbox Column -->
              <ng-container matColumnDef="select" *ngIf="canManageBooks()">
                <th mat-header-cell *matHeaderCellDef>
                  <mat-checkbox
                    (change)="$event ? masterToggle() : null"
                    [checked]="selection.hasValue() && isAllSelected()"
                    [indeterminate]="selection.hasValue() && !isAllSelected()"
                    matTooltip="Chọn tất cả">
                  </mat-checkbox>
                </th>
                <td mat-cell *matCellDef="let book">
                  <mat-checkbox
                    (click)="$event.stopPropagation()"
                    (change)="$event ? selection.toggle(book) : null"
                    [checked]="selection.isSelected(book)">
                  </mat-checkbox>
                </td>
              </ng-container>

              <!-- Book Info Column -->
              <ng-container matColumnDef="title">
                <th mat-header-cell *matHeaderCellDef>Thông tin sách</th>
                <td mat-cell *matCellDef="let book">
                  <div class="book-cell">
                    <div class="book-image">
                      <img *ngIf="book.imageUrl" [src]="book.imageUrl" [alt]="book.title" />
                      <mat-icon *ngIf="!book.imageUrl" class="book-placeholder">menu_book</mat-icon>
                    </div>
                    <div class="book-details">
                      <h4 class="book-title">{{ book.title }}</h4>
                      <p class="book-author">{{ book.author }}</p>
                      <div class="book-meta">
                        <mat-chip-set>
                          <mat-chip>{{ book.categoryName }}</mat-chip>
                          <mat-chip *ngIf="book.isbn">ISBN: {{ book.isbn }}</mat-chip>
                        </mat-chip-set>
                      </div>
                    </div>
                  </div>
                </td>
              </ng-container>

              <!-- Quantity Column -->
              <ng-container matColumnDef="quantity">
                <th mat-header-cell *matHeaderCellDef>Số lượng</th>
                <td mat-cell *matCellDef="let book">
                  <div class="quantity-info">
                    <div class="quantity-item">
                      <span class="quantity-label">Tổng:</span>
                      <span class="quantity-value total">{{ book.quantity }}</span>
                    </div>
                    <div class="quantity-item">
                      <span class="quantity-label">Trong kho:</span>
                      <span class="quantity-value stock">{{ book.stockQuantity }}</span>
                    </div>
                    <div class="quantity-item">
                      <span class="quantity-label">Trên kệ:</span>
                      <span class="quantity-value shelf">{{ book.onShelfQuantity }}</span>
                    </div>
                  </div>
                </td>
              </ng-container>
              <!-- Actions Column -->
              <ng-container matColumnDef="actions" *ngIf="canManageBooks()">
                <th mat-header-cell *matHeaderCellDef>Thao tác</th>
                <td mat-cell *matCellDef="let book">
                  <div class="action-buttons">
                    <button
                      type="button"
                      mat-icon-button
                      [routerLink]="['/books/edit', book.id]"
                      color="primary"
                      matTooltip="Chỉnh sửa">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button
                      type="button"
                      mat-icon-button
                      (click)="deleteBook(book)"
                      color="warn"
                      matTooltip="Xóa">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [
    `
      .search-section {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 20px;
      }

      .search-field {
        flex: 1;
        max-width: 500px;
      }

      .table-container {
        overflow-x: auto;
      }

      .mat-mdc-table {
        width: 100%;
      }

      .no-data {
        text-align: center;
        padding: 40px;
        color: #666;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
      }

      .no-data mat-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #ccc;
      }

      mat-card-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
      }

      .book-list-container {
        padding: 0;
        background: var(--color-background);
        min-height: calc(100vh - 64px);
      }

      .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-xl);
        background: var(--color-surface);
        border-bottom: 1px solid var(--color-outline);
        box-shadow: var(--shadow-sm);
      }

      .page-header .header-content .page-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        margin: 0 0 var(--spacing-xs) 0;
        font-size: var(--font-size-3xl);
        font-weight: var(--font-weight-bold);
        color: var(--color-on-surface);
      }

      .page-header .header-content .page-title .title-icon {
        font-size: 36px;
        width: 36px;
        height: 36px;
        color: var(--color-primary);
      }

      .page-header .header-content .page-subtitle {
        margin: 0;
        color: var(--color-on-surface);
        opacity: 0.7;
        font-size: var(--font-size-lg);
      }

      .search-card {
        margin: var(--spacing-lg);
        margin-bottom: 0;
      }

      .search-section {
        display: flex;
        gap: var(--spacing-md);
        align-items: flex-end;
        margin-bottom: var(--spacing-lg);
      }

      .search-field {
        flex: 1;
      }

      .search-actions {
        display: flex;
        gap: var(--spacing-sm);
      }

      .bulk-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-md);
        background: linear-gradient(135deg, var(--color-primary), var(--color-primary-variant));
        color: var(--color-on-primary);
        border-radius: var(--radius-medium);
        margin-top: var(--spacing-lg);
      }

      .selection-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-weight: var(--font-weight-medium);
      }

      .bulk-actions-buttons {
        display: flex;
        gap: var(--spacing-sm);
      }

      .table-card {
        margin: var(--spacing-lg);
        overflow: hidden;
      }

      .books-table {
        width: 100%;
      }

      .book-cell {
        display: flex;
        gap: var(--spacing-md);
        align-items: center;
        padding: var(--spacing-sm) 0;

        .book-image {
          width: 60px;
          height: 80px;
          border-radius: var(--radius-small);
          overflow: hidden;
          background: var(--color-surface-variant);
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          border: 1px solid var(--color-outline);

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
          }

          .book-placeholder {
            font-size: 32px;
            width: 32px;
            height: 32px;
            color: var(--color-on-surface);
            opacity: 0.5;
          }
        }

        .book-details {
          flex: 1;
          min-width: 0;
        }
      }

      .book-details .book-title {
        margin: 0 0 var(--spacing-xs) 0;
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--color-on-surface);
      }

      .book-details .book-author {
        margin: 0 0 var(--spacing-sm) 0;
        color: var(--color-on-surface);
        opacity: 0.8;
      }

      .quantity-info {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
      }

      .quantity-item {
        display: flex;
        justify-content: space-between;
        font-size: var(--font-size-sm);
        padding: 2px 8px;
        border-radius: var(--radius-small);
        background-color: var(--color-surface-variant);
        margin-bottom: 2px;
      }

      .quantity-label {
        font-weight: var(--font-weight-medium);
        color: var(--color-on-surface-variant);
      }

      .quantity-value {
        font-weight: var(--font-weight-semibold);
      }

      .quantity-value.total {
        color: var(--color-primary);
      }

      .quantity-value.stock {
        color: var(--color-success);
      }

      .quantity-value.shelf {
        color: var(--color-warning);
      }

      @media (max-width: 768px) {
        .page-header {
          flex-direction: column;
          align-items: stretch;
          gap: var(--spacing-md);
        }

        .search-section {
          flex-direction: column;
          align-items: stretch;
        }

        .bulk-actions {
          flex-direction: column;
          gap: var(--spacing-md);
        }
      }
    `,
  ],
})
export class BookListComponent implements OnInit {
  books: Book[] = [];
  displayedColumns: string[] = [];
  searchTerm: string = "";
  selection = new SelectionModel<Book>(true, []);
  isDeleting = false;
  isLoading = false;

  constructor(
    private bookService: BookService,
    private authService: AuthService,
    private themeService: ThemeService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.updateDisplayedColumns();
  }

  ngOnInit(): void {
    this.loadBooks();
  }

  canManageBooks(): boolean {
    return this.authService.canManageBooks();
  }

  private updateDisplayedColumns(): void {
    this.displayedColumns = this.canManageBooks()
      ? ["select", "title", "quantity", "actions"]
      : ["title", "quantity"];
  }

  loadBooks(): void {
    this.isLoading = true;
    this.bookService.getBooks().subscribe({
      next: (books) => {
        this.books = books;
        this.selection.clear();
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open("Lỗi khi tải danh sách sách", "Đóng", {
          duration: 3000,
        });
        this.isLoading = false;
      },
    });
  }

  navigateToAdd(): void {
    // This will be handled by router
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.loadBooks();
  }

  onSearch(): void {
    if (this.searchTerm.trim()) {
      this.isLoading = true;
      this.bookService.searchBooksGeneral(this.searchTerm.trim()).subscribe({
        next: (books) => {
          this.books = books;
          this.selection.clear();
          this.isLoading = false;
        },
        error: (error) => {
          this.snackBar.open("Lỗi khi tìm kiếm sách", "Đóng", {
            duration: 3000,
          });
          this.isLoading = false;
        },
      });
    } else {
      this.loadBooks();
    }
  }



  // Selection methods
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.books.length;
    return numSelected === numRows;
  }

  masterToggle(): void {
    this.isAllSelected()
      ? this.selection.clear()
      : this.books.forEach(book => this.selection.select(book));
  }

  selectAll(): void {
    this.books.forEach(book => this.selection.select(book));
  }

  clearSelection(): void {
    this.selection.clear();
  }

  // Delete methods
  deleteBook(book: Book): void {
    if (confirm(`Bạn có chắc chắn muốn xóa sách "${book.title}"?`)) {
      this.bookService.deleteBook(book.id).subscribe({
        next: () => {
          this.snackBar.open("Xóa sách thành công", "Đóng", {
            duration: 3000,
          });
          this.loadBooks();
        },
        error: (error) => {
          let message = "Lỗi khi xóa sách";
          if (error.error) {
            if (typeof error.error === "string") {
              message = error.error;
            } else if (error.error.message) {
              message = error.error.message;
            } else if (error.error.title) {
              message = error.error.title;
            }
          } else if (error.message) {
            message = error.message;
          }

          this.snackBar.open(message, "Đóng", {
            duration: 5000,
          });
        },
      });
    }
  }

  deleteSelectedBooks(): void {
    const selectedBooks = this.selection.selected;
    const bookTitles = selectedBooks.map(book => book.title).join(', ');
    
    if (confirm(`Bạn có chắc chắn muốn xóa ${selectedBooks.length} sách đã chọn?\n\n${bookTitles}`)) {
      this.isDeleting = true;
      const bookIds = selectedBooks.map(book => book.id);
      
      this.bookService.deleteMultipleBooks(bookIds).subscribe({
        next: (result) => {
          this.snackBar.open(result.message, "Đóng", {
            duration: 5000,
          });
          
          if (result.failedReasons && result.failedReasons.length > 0) {
            console.warn('Một số sách không thể xóa:', result.failedReasons);
          }
          
          this.loadBooks();
          this.isDeleting = false;
        },
        error: (error) => {
          let message = "Lỗi khi xóa sách";
          if (error.error?.message) {
            message = error.error.message;
          }
          
          this.snackBar.open(message, "Đóng", {
            duration: 5000,
          });
          this.isDeleting = false;
        },
      });
    }
  }

  deleteAllBooks(): void {
    if (this.books.length === 0) {
      this.snackBar.open("Không có sách nào để xóa", "Đóng", {
        duration: 3000,
      });
      return;
    }

    const confirmMessage = `⚠️ CẢNH BÁO: Bạn đang chuẩn bị xóa TẤT CẢ ${this.books.length} sách!\n\nHành động này không thể hoàn tác. Bạn có chắc chắn muốn tiếp tục?`;
    
    if (confirm(confirmMessage)) {
      this.isDeleting = true;
      const allBookIds = this.books.map(book => book.id);
      
      this.bookService.deleteMultipleBooks(allBookIds).subscribe({
        next: (result) => {
          this.snackBar.open(result.message, "Đóng", {
            duration: 5000,
          });
          
          if (result.failedReasons && result.failedReasons.length > 0) {
            console.warn('Một số sách không thể xóa:', result.failedReasons);
          }
          
          this.loadBooks();
          this.isDeleting = false;
        },
        error: (error) => {
          let message = "Lỗi khi xóa sách";
          if (error.error?.message) {
            message = error.error.message;
          }
          
          this.snackBar.open(message, "Đóng", {
            duration: 5000,
          });
          this.isDeleting = false;
        },
      });
    }
  }
}
