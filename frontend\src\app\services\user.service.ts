import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { 
  UserManagement, 
  CreateUser, 
  UpdateUser, 
  ChangePassword 
} from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private apiUrl = `${environment.apiUrl}/users`;

  constructor(private http: HttpClient) { }

  getUsers(): Observable<UserManagement[]> {
    return this.http.get<UserManagement[]>(this.apiUrl);
  }

  getUser(id: number): Observable<UserManagement> {
    return this.http.get<UserManagement>(`${this.apiUrl}/${id}`);
  }

  createUser(user: CreateUser): Observable<UserManagement> {
    return this.http.post<UserManagement>(this.apiUrl, user);
  }

  updateUser(id: number, user: UpdateUser): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, user);
  }

  deleteUser(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  changePassword(id: number, changePassword: ChangePassword): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/change-password`, changePassword);
  }

  toggleUserStatus(id: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/toggle-status`, {});
  }

  forceVerifyEmail(id: number): Observable<{ message: string }> {
    return this.http.post<{ message: string }>(`${this.apiUrl}/${id}/force-verify`, {});
  }

  searchUsers(params: {
    username?: string;
    email?: string;
    firstName?: string;
    lastName?: string;
    role?: number;
    isActive?: boolean;
  }): Observable<UserManagement[]> {
    return this.http.get<UserManagement[]>(`${this.apiUrl}/search`, { params: { ...params } });
  }
} 