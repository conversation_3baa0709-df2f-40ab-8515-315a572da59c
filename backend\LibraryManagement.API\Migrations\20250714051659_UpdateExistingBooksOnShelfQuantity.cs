﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LibraryManagement.API.Migrations
{
    /// <inheritdoc />
    public partial class UpdateExistingBooksOnShelfQuantity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Update existing books on shelves: move StockQuantity to OnShelfQuantity
            migrationBuilder.Sql(@"
                UPDATE Books
                SET OnShelfQuantity = StockQuantity,
                    StockQuantity = 0
                WHERE BookshelfId IS NOT NULL AND OnShelfQuantity = 0;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Rollback: move OnShelfQuantity back to StockQuantity
            migrationBuilder.Sql(@"
                UPDATE Books
                SET StockQuantity = OnShelfQuantity,
                    OnShelfQuantity = 0
                WHERE BookshelfId IS NOT NULL;
            ");
        }
    }
}
