using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using LibraryManagement.Application.DTOs;
using LibraryManagement.Core.Interfaces;
using System.ComponentModel.DataAnnotations;

namespace LibraryManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[AllowAnonymous] // Allow anonymous access for all endpoints
public class AuthController : ControllerBase
{
    private readonly IAuthService _authService;

    public AuthController(IAuthService authService)
    {
        _authService = authService;
    }

    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] LoginDto loginDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _authService.LoginAsync(loginDto.Email, loginDto.Password);
        
        if (!result.Success)
        {
            return BadRequest(new { message = result.Message });
        }

        var userDto = new UserDto
        {
            Id = result.User!.Id,
            Username = result.User.Username,
            Email = result.User.Email,
            FirstName = result.User.FirstName,
            LastName = result.User.LastName,
            Role = result.User.Role.ToString()
        };

        var response = new AuthResponseDto
        {
            Token = result.Token,
            User = userDto
        };

        return Ok(response);
    }

    [HttpPost("register")]
    public async Task<IActionResult> Register([FromBody] RegisterDto registerDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        // Validate password confirmation
        if (registerDto.Password != registerDto.ConfirmPassword)
        {
            return BadRequest(new { message = "Mật khẩu xác nhận không khớp" });
        }

        // Validate password strength
        if (registerDto.Password.Length < 6)
        {
            return BadRequest(new { message = "Mật khẩu phải có ít nhất 6 ký tự" });
        }

        var result = await _authService.RegisterAsync(
            registerDto.Email, 
            registerDto.Password, 
            registerDto.Username,
            registerDto.FirstName, 
            registerDto.LastName
        );

        if (!result.Success)
        {
            return BadRequest(new { message = result.Message });
        }

        var userDto = new UserDto
        {
            Id = result.User!.Id,
            Username = result.User.Username,
            Email = result.User.Email,
            FirstName = result.User.FirstName,
            LastName = result.User.LastName,
            Role = result.User.Role.ToString()
        };

        var response = new AuthResponseDto
        {
            Token = result.Token,
            User = userDto
        };

        return Ok(response);
    }

    [HttpPost("verify-email")]
    public async Task<IActionResult> VerifyEmail([FromBody] VerifyEmailDto verifyEmailDto)
    {
        if (string.IsNullOrEmpty(verifyEmailDto.Token))
        {
            return BadRequest(new { message = "Token là bắt buộc" });
        }

        var result = await _authService.VerifyEmailAsync(verifyEmailDto.Token);
        
        if (!result.Success)
        {
            return BadRequest(new { message = result.Message });
        }

        return Ok(new { message = result.Message });
    }

    [HttpPost("request-password-reset")]
    public async Task<IActionResult> RequestPasswordReset([FromBody] RequestPasswordResetDto requestDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _authService.RequestPasswordResetAsync(requestDto.Email);
        
        // Always return success for security (don't reveal if email exists)
        return Ok(new { message = result.Message });
    }

    [HttpPost("reset-password")]
    public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordDto resetDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        if (resetDto.Password != resetDto.ConfirmPassword)
        {
            return BadRequest(new { message = "Mật khẩu xác nhận không khớp" });
        }

        if (resetDto.Password.Length < 6)
        {
            return BadRequest(new { message = "Mật khẩu phải có ít nhất 6 ký tự" });
        }

        var result = await _authService.ResetPasswordAsync(resetDto.Token, resetDto.Password);
        
        if (!result.Success)
        {
            return BadRequest(new { message = result.Message });
        }

        return Ok(new { message = result.Message });
    }

    [HttpPost("resend-verification")]
    public async Task<IActionResult> ResendVerification([FromBody] ResendVerificationDto resendDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _authService.ResendVerificationEmailAsync(resendDto.Email);
        
        if (!result.Success)
        {
            return BadRequest(new { message = result.Message });
        }

        return Ok(new { message = result.Message });
    }
}

// Additional DTOs for new endpoints
public class VerifyEmailDto
{
    [Required(ErrorMessage = "Token là bắt buộc")]
    public string Token { get; set; } = string.Empty;
}

public class RequestPasswordResetDto
{
    [Required(ErrorMessage = "Email là bắt buộc")]
    [EmailAddress(ErrorMessage = "Email không hợp lệ")]
    public string Email { get; set; } = string.Empty;
}

public class ResetPasswordDto
{
    [Required(ErrorMessage = "Token là bắt buộc")]
    public string Token { get; set; } = string.Empty;

    [Required(ErrorMessage = "Mật khẩu là bắt buộc")]
    [MinLength(6, ErrorMessage = "Mật khẩu phải có ít nhất 6 ký tự")]
    public string Password { get; set; } = string.Empty;

    [Required(ErrorMessage = "Xác nhận mật khẩu là bắt buộc")]
    public string ConfirmPassword { get; set; } = string.Empty;
}

public class ResendVerificationDto
{
    [Required(ErrorMessage = "Email là bắt buộc")]
    [EmailAddress(ErrorMessage = "Email không hợp lệ")]
    public string Email { get; set; } = string.Empty;
} 