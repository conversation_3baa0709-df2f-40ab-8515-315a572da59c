import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormsModule } from '@angular/forms';
import { Member, MemberStatus, MEMBER_STATUS_OPTIONS } from '../../models/member.model';
import { MemberService } from '../../services/member.service';
import { AuthService } from '../../services/auth.service';
import { MemberBorrowHistoryDialogComponent } from './member-borrow-history-dialog.component';

@Component({
  selector: 'app-member-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatSnackBarModule,
    MatChipsModule,
    MatDialogModule,
    MatTooltipModule,
    FormsModule
  ],
  template: `
    <mat-card>
      <mat-card-header>
        <mat-card-title>Quản lý Thành viên</mat-card-title>
        <div class="spacer"></div>
        <button *ngIf="canManageMembers()" mat-raised-button color="primary" routerLink="/members/add">
          <mat-icon>person_add</mat-icon>
          Thêm thành viên mới
        </button>
      </mat-card-header>

      <mat-card-content>
        <!-- Search filters -->
        <div class="search-filters">
          <mat-form-field class="search-field">
            <mat-label>Tìm kiếm</mat-label>
            <input matInput [(ngModel)]="searchTerm" (input)="onSearch()" placeholder="Nhập tên, email hoặc số điện thoại...">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <mat-form-field>
            <mat-label>Trạng thái</mat-label>
            <mat-select [(ngModel)]="searchStatus" (selectionChange)="onSearch()">
              <mat-option [value]="undefined">Tất cả</mat-option>
              <mat-option *ngFor="let status of statusOptions" [value]="status.value">
                {{status.label}}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <button mat-icon-button (click)="clearSearch()" *ngIf="hasActiveFilters()" matTooltip="Xóa tìm kiếm">
            <mat-icon>clear</mat-icon>
          </button>
        </div>

        <!-- Members table -->
        <div class="table-container">
          <table mat-table [dataSource]="members" class="mat-elevation-z8">
            <ng-container matColumnDef="fullName">
              <th mat-header-cell *matHeaderCellDef>Họ và tên</th>
              <td mat-cell *matCellDef="let member">{{member.fullName}}</td>
            </ng-container>

            <ng-container matColumnDef="email">
              <th mat-header-cell *matHeaderCellDef>Email</th>
              <td mat-cell *matCellDef="let member">{{member.email}}</td>
            </ng-container>

            <ng-container matColumnDef="phone">
              <th mat-header-cell *matHeaderCellDef>Điện thoại</th>
              <td mat-cell *matCellDef="let member">{{member.phone || 'Chưa có'}}</td>
            </ng-container>

            <ng-container matColumnDef="membershipDate">
              <th mat-header-cell *matHeaderCellDef>Ngày tham gia</th>
              <td mat-cell *matCellDef="let member">{{formatDate(member.membershipDate)}}</td>
            </ng-container>

            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Trạng thái</th>
              <td mat-cell *matCellDef="let member">
                <mat-chip-set>
                  <mat-chip [class]="getStatusClass(member.status)">
                    {{member.statusName}}
                  </mat-chip>
                </mat-chip-set>
              </td>
            </ng-container>

            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Thao tác</th>
                             <td mat-cell *matCellDef="let member">
                 <button *ngIf="canHandleBorrowOperations()" mat-icon-button (click)="viewBorrowHistory(member)" 
                         matTooltip="Xem lịch sử mượn sách" color="accent">
                   <mat-icon>history</mat-icon>
                 </button>
                 <button *ngIf="canManageMembers()" mat-icon-button [routerLink]="['/members/edit', member.id]" 
                         matTooltip="Chỉnh sửa" color="primary">
                   <mat-icon>edit</mat-icon>
                 </button>
                 <button *ngIf="canManageMembers()" mat-icon-button (click)="deleteMember(member)" 
                         matTooltip="Xóa" color="warn">
                   <mat-icon>delete</mat-icon>
                 </button>
               </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
        </div>

        <div *ngIf="members.length === 0" class="no-data">
          <mat-icon>people_outline</mat-icon>
          <p>Không tìm thấy thành viên nào.</p>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .search-filters {
      display: flex;
      gap: 16px;
      margin-bottom: 20px;
      flex-wrap: wrap;
      align-items: center;
    }

    .search-field {
      flex: 1;
      max-width: 500px;
    }

    .search-filters mat-form-field {
      min-width: 180px;
    }

    .table-container {
      overflow-x: auto;
    }

    .mat-mdc-table {
      width: 100%;
    }

    .no-data {
      text-align: center;
      padding: 40px;
      color: #666;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
    }

    .no-data mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      opacity: 0.5;
    }

    mat-card-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }

    .spacer {
      flex: 1 1 auto;
    }

    .status-active {
      background-color: #4caf50 !important;
      color: white;
    }

    .status-suspended {
      background-color: #ff9800 !important;
      color: white;
    }

    .status-expired {
      background-color: #f44336 !important;
      color: white;
    }

    .status-banned {
      background-color: #9c27b0 !important;
      color: white;
    }

    mat-chip-set {
      display: flex;
    }
  `]
})
export class MemberListComponent implements OnInit {
  members: Member[] = [];
  displayedColumns: string[] = ['fullName', 'email', 'phone', 'membershipDate', 'status', 'actions'];
  
  // Search filters
  searchTerm: string = '';
  searchStatus?: MemberStatus;
  
  statusOptions = MEMBER_STATUS_OPTIONS;

  constructor(
    private memberService: MemberService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadMembers();
  }

  canManageMembers(): boolean {
    return this.authService.canManageMembers();
  }

  canHandleBorrowOperations(): boolean {
    return this.authService.canHandleBorrowOperations();
  }

  loadMembers(): void {
    this.memberService.getMembers().subscribe({
      next: (members) => {
        this.members = members;
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi tải danh sách thành viên', 'Đóng', {
          duration: 3000
        });
      }
    });
  }

  onSearch(): void {
    if (this.hasActiveFilters()) {
      this.memberService.searchMembers(
        this.searchTerm || undefined,
        undefined, 
        undefined,
        this.searchStatus
      ).subscribe({
        next: (members) => {
          this.members = members;
        },
        error: (error) => {
          this.snackBar.open('Lỗi khi tìm kiếm thành viên', 'Đóng', {
            duration: 3000
          });
        }
      });
    } else {
      this.loadMembers();
    }
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.searchStatus = undefined;
    this.loadMembers();
  }

  hasActiveFilters(): boolean {
    return !!(this.searchTerm || this.searchStatus !== undefined);
  }

  deleteMember(member: Member): void {
    if (confirm(`Bạn có chắc chắn muốn xóa thành viên "${member.fullName}"?`)) {
      this.memberService.deleteMember(member.id).subscribe({
        next: () => {
          this.snackBar.open('Xóa thành viên thành công', 'Đóng', {
            duration: 3000
          });
          this.loadMembers();
        },
        error: (error) => {
          // Handle different error response formats from backend
          let message = 'Lỗi khi xóa thành viên';
          
          if (error.error) {
            // If error.error is a string (from BadRequest(string))
            if (typeof error.error === 'string') {
              message = error.error;
            }
            // If error.error is an object with message property
            else if (error.error.message) {
              message = error.error.message;
            }
            // If error.error is an object with title property (ValidationProblem)
            else if (error.error.title) {
              message = error.error.title;
            }
          }
          // Fallback to error.message
          else if (error.message) {
            message = error.message;
          }
          
          this.snackBar.open(message, 'Đóng', {
            duration: 5000
          });
        }
      });
    }
  }

  viewBorrowHistory(member: Member): void {
    this.dialog.open(MemberBorrowHistoryDialogComponent, {
      width: '900px',
      maxWidth: '90vw',
      data: { member }
    });
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  }

  getStatusClass(status: MemberStatus): string {
    switch (status) {
      case MemberStatus.Active:
        return 'status-active';
      case MemberStatus.Suspended:
        return 'status-suspended';
      case MemberStatus.Expired:
        return 'status-expired';
      case MemberStatus.Banned:
        return 'status-banned';
      default:
        return '';
    }
  }
} 