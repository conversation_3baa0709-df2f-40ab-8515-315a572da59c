<!DOCTYPE html>
<html>
<head>
    <title>Migrate Shelf Quantities</title>
</head>
<body>
    <h1>Migrate Shelf Quantities</h1>
    <button onclick="migrateData()">Run Migration</button>
    <div id="result"></div>

    <script>
        async function migrateData() {
            try {
                const response = await fetch('https://localhost:7001/api/books/migrate-shelf-quantities', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const result = await response.json();
                document.getElementById('result').innerHTML = 
                    '<h3>Result:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<h3>Error:</h3><pre>' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
