namespace LibraryManagement.Core.Entities;

public class Book : BaseEntity
{
    public string Title { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string? ISBN { get; set; }
    public string? Publisher { get; set; }
    public DateTime? PublishedDate { get; set; }
    public int CategoryId { get; set; }
    public int Quantity { get; set; } // Tổng số lượng
    public int StockQuantity { get; set; } // Số lượng trong kho
    public int OnShelfQuantity { get; set; } // Số lượng trên kệ
    public string? Description { get; set; }
    public string? ImageUrl { get; set; }
    public decimal? Price { get; set; }
    
    // Shelf location (simplified)
    public int? BookshelfId { get; set; }
    public string? LocationCode { get; set; } // Simple location like "A1", "B2"

    // Navigation properties
    public virtual Category Category { get; set; } = null!;
    public virtual Bookshelf? Bookshelf { get; set; }
    public virtual ICollection<BorrowRecord> BorrowRecords { get; set; } = new List<BorrowRecord>();

}