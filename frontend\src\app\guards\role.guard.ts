import { inject } from '@angular/core';
import { Router, CanActivateFn } from '@angular/router';
import { AuthService } from '../services/auth.service';

// Generic role guard factory
export function createRoleGuard(allowedRoles: string[]): CanActivateFn {
  return (route, state) => {
    const authService = inject(AuthService);
    const router = inject(Router);

    if (!authService.isAuthenticated) {
      router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
      return false;
    }

    if (!authService.hasAnyRole(allowedRoles)) {
      router.navigate(['/not-authenticated']);
      return false;
    }

    return true;
  };
}

// Specific role guards
export const adminGuard: CanActivateFn = createRoleGuard(['Admin']);

export const librarianOrHigherGuard: CanActivateFn = createRoleGuard(['Admin', 'Librarian']);

export const staffGuard: CanActivateFn = createRoleGuard(['Admin', 'Librarian', 'Assistant']);

// Feature-specific guards
export const bookManagementGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  if (!authService.isAuthenticated) {
    router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
    return false;
  }

  if (!authService.canManageBooks()) {
    router.navigate(['/not-authenticated']);
    return false;
  }

  return true;
};

export const memberManagementGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  if (!authService.isAuthenticated) {
    router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
    return false;
  }

  if (!authService.canManageMembers()) {
    router.navigate(['/not-authenticated']);
    return false;
  }

  return true;
};

export const borrowOperationsGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  if (!authService.isAuthenticated) {
    router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
    return false;
  }

  if (!authService.canHandleBorrowOperations()) {
    router.navigate(['/not-authenticated']);
    return false;
  }

  return true;
}; 