import { Directive, Input, TemplateRef, ViewContainerRef, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Directive({
  selector: '[appHasPermission]',
  standalone: true
})
export class HasPermissionDirective implements OnInit, OnDestroy {
  private subscription: Subscription = new Subscription();
  private currentPermission: string = '';

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private authService: AuthService
  ) {}

  @Input() set appHasPermission(permission: string) {
    this.currentPermission = permission;
    this.updateView();
  }

  ngOnInit() {
    // Subscribe to user changes to update view when user logs in/out
    this.subscription = this.authService.currentUser$.subscribe(() => {
      this.updateView();
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  private updateView(): void {
    if (this.hasPermission()) {
      if (!this.viewContainer.length) {
        this.viewContainer.createEmbeddedView(this.templateRef);
      }
    } else {
      this.viewContainer.clear();
    }
  }

  private hasPermission(): boolean {
    if (!this.authService.isAuthenticated) {
      return false;
    }

    switch (this.currentPermission.toLowerCase()) {
      case 'admin':
        return this.authService.isAdmin();
      
      case 'librarian':
        return this.authService.isLibrarian();
      
      case 'assistant':
        return this.authService.isAssistant();
      
      case 'librarian-or-higher':
        return this.authService.hasAnyRole(['Admin', 'Librarian']);
      
      case 'staff':
        return this.authService.hasAnyRole(['Admin', 'Librarian', 'Assistant']);
      
      case 'book-management':
        return this.authService.canManageBooks();
      
      case 'member-management':
        return this.authService.canManageMembers();
      
      case 'borrow-operations':
        return this.authService.canHandleBorrowOperations();
      
      case 'admin-features':
        return this.authService.canAccessAdminFeatures();
      
      default:
        return false;
    }
  }
} 