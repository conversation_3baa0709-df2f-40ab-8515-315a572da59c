import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-verify-email',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  template: `
    <div class="verify-container">
      <mat-card class="verify-card">
        <mat-card-header>
          <mat-card-title><PERSON><PERSON><PERSON> nh<PERSON>n <PERSON>ail</mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <div class="verify-content" *ngIf="!isLoading">
            <div class="icon-container" [class.success]="isSuccess" [class.error]="!isSuccess">
              <mat-icon>{{isSuccess ? 'check_circle' : 'error'}}</mat-icon>
            </div>
            
            <h3>{{title}}</h3>
            <p>{{message}}</p>
            
            <div class="actions" *ngIf="isSuccess">
              <p class="note">
                <mat-icon>info</mat-icon>
                Để cập nhật trạng thái email đã xác nhận, bạn cần đăng nhập lại.
              </p>
              <button mat-raised-button color="primary" routerLink="/login">
                <mat-icon>login</mat-icon>
                Đăng nhập ngay
              </button>
            </div>
            
            <div class="actions" *ngIf="!isSuccess && showResendButton">
              <button mat-button color="primary" (click)="resendVerification()" [disabled]="isResending">
                <mat-icon>refresh</mat-icon>
                {{isResending ? 'Đang gửi...' : 'Gửi lại email xác nhận'}}
              </button>
              <button mat-button routerLink="/register">
                <mat-icon>person_add</mat-icon>
                Đăng ký lại
              </button>
            </div>
          </div>
          
          <div class="loading-content" *ngIf="isLoading">
            <mat-spinner diameter="50"></mat-spinner>
            <p>Đang xác nhận email...</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .verify-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background-color: #f5f5f5;
    }

    .verify-card {
      width: 100%;
      max-width: 500px;
      padding: 20px;
      text-align: center;
    }

    mat-card-header {
      display: flex;
      justify-content: center;
      margin-bottom: 30px;
    }

    mat-card-title {
      font-size: 24px;
      font-weight: 500;
    }

    .verify-content, .loading-content {
      text-align: center;
    }

    .icon-container {
      margin: 20px 0;
    }

    .icon-container mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
    }

    .icon-container.success mat-icon {
      color: #4caf50;
    }

    .icon-container.error mat-icon {
      color: #f44336;
    }

    h3 {
      margin: 20px 0 10px 0;
      color: #333;
    }

    p {
      color: #666;
      margin-bottom: 30px;
      line-height: 1.5;
    }

    .actions {
      display: flex;
      flex-direction: column;
      gap: 12px;
      align-items: center;
    }

    .actions button {
      min-width: 200px;
    }

    .loading-content {
      padding: 40px 20px;
    }

    .loading-content p {
      margin-top: 20px;
      color: #666;
    }

    .note {
      display: flex;
      align-items: center;
      gap: 8px;
      background-color: #e3f2fd;
      padding: 12px;
      border-radius: 4px;
      margin-bottom: 20px;
      color: #1976d2;
      font-size: 14px;
    }

    .note mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  `]
})
export class VerifyEmailComponent implements OnInit {
  isLoading = true;
  isSuccess = false;
  isResending = false;
  title = '';
  message = '';
  showResendButton = false;
  userEmail = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    const token = this.route.snapshot.queryParams['token'];
    
    if (!token) {
      this.handleError('Token xác nhận không hợp lệ');
      return;
    }

    this.verifyEmail(token);
  }

  verifyEmail(token: string): void {
    this.authService.verifyEmail({ token }).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.isSuccess = true;
        this.title = 'Xác nhận thành công!';
        this.message = response.message + ' Vui lòng đăng nhập lại để cập nhật trạng thái.';
        
        // Logout user để buộc đăng nhập lại với token mới
        this.authService.logout();
      },
      error: (error) => {
        this.isLoading = false;
        this.isSuccess = false;
        this.title = 'Xác nhận thất bại';
        this.message = error.error?.message || 'Có lỗi xảy ra trong quá trình xác nhận email';
        this.showResendButton = true;
      }
    });
  }

  resendVerification(): void {
    if (!this.userEmail) {
      this.snackBar.open('Vui lòng nhập email để gửi lại', 'Đóng', { duration: 3000 });
      return;
    }

    this.isResending = true;
    this.authService.resendVerification({ email: this.userEmail }).subscribe({
      next: (response) => {
        this.isResending = false;
        this.snackBar.open(response.message, 'Đóng', { duration: 5000 });
      },
      error: (error) => {
        this.isResending = false;
        const message = error.error?.message || 'Không thể gửi lại email xác nhận';
        this.snackBar.open(message, 'Đóng', { duration: 3000 });
      }
    });
  }

  private handleError(message: string): void {
    this.isLoading = false;
    this.isSuccess = false;
    this.title = 'Lỗi xác nhận';
    this.message = message;
  }
} 