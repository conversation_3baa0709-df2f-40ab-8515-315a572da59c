-- Library Management Complete Sample Data
-- <PERSON><PERSON><PERSON> để thêm dữ liệu mẫu hoàn chỉnh cho ứng dụng quản lý thư viện

USE LibraryManagementDb;
GO

-- X<PERSON>a dữ liệu cũ (theo thứ tự để tránh FK constraint)
DELETE FROM ShelfSlotBooks;
DELETE FROM ShelfSlots;
DELETE FROM BorrowRecords;
DELETE FROM Bookshelves;
DELETE FROM Zones;
DELETE FROM Books;
DELETE FROM Members;
DELETE FROM Users;
DELETE FROM Categories;

-- Reset Identity seeds
DBCC CHECKIDENT ('ShelfSlotBooks', RESEED, 0);
DBCC CHECKIDENT ('ShelfSlots', RESEED, 0);
DBCC CHECKIDENT ('BorrowRecords', RESEED, 0);
DBCC CHECKIDENT ('Bookshelves', RESEED, 0);
DBCC CHECKIDENT ('Zones', RESEED, 0);
DBCC CHECKIDENT ('Books', RESEED, 0);
DBCC CHECKIDENT ('Members', RESEED, 0);
DBCC CHECKIDENT ('Users', RESEED, 0);
DBCC CHECKIDENT ('Categories', RESEED, 0);

PRINT 'Deleted all existing data and reset identity seeds.';

-- =============================================
-- 1. THÊM CATEGORIES
-- =============================================
SET IDENTITY_INSERT Categories ON;
INSERT INTO Categories (Id, Name, Description, CreatedAt) VALUES
(1, N'Công nghệ thông tin', N'Sách về lập trình, phần mềm và công nghệ', GETUTCDATE()),
(2, N'Văn học Việt Nam', N'Tác phẩm văn học của các tác giả Việt Nam', GETUTCDATE()),
(3, N'Văn học nước ngoài', N'Tác phẩm văn học được dịch từ nước ngoài', GETUTCDATE()),
(4, N'Khoa học', N'Sách về khoa học tự nhiên và ứng dụng', GETUTCDATE()),
(5, N'Kinh doanh', N'Sách về quản lý, kinh doanh và tài chính', GETUTCDATE()),
(6, N'Lịch sử', N'Sách về lịch sử Việt Nam và thế giới', GETUTCDATE());
SET IDENTITY_INSERT Categories OFF;

-- =============================================
-- 2. THÊM ZONES
-- =============================================
SET IDENTITY_INSERT Zones ON;
INSERT INTO Zones (Id, Name, Description, CreatedAt) VALUES
(1, N'Tầng 1 - Khu A', N'Khu vực chính tầng 1, gần lối vào', GETUTCDATE()),
(2, N'Tầng 1 - Khu B', N'Khu vực phía sau tầng 1, yên tĩnh', GETUTCDATE()),
(3, N'Tầng 2 - Khu IT', N'Khu vực chuyên về sách công nghệ thông tin', GETUTCDATE()),
(4, N'Tầng 2 - Khu Văn học', N'Khu vực dành cho sách văn học', GETUTCDATE()),
(5, N'Tầng 3 - Khu Khoa học', N'Khu vực sách khoa học và kỹ thuật', GETUTCDATE());
SET IDENTITY_INSERT Zones OFF;

-- =============================================
-- 3. THÊM USERS
-- =============================================
SET IDENTITY_INSERT Users ON;
INSERT INTO Users (Id, Username, Email, PasswordHash, FirstName, LastName, Role, IsActive, EmailVerified, CreatedAt) VALUES
(1, 'admin', '<EMAIL>', '$2b$10$dummyHashForAdmin123456789', N'Admin', N'System', 1, 1, 1, GETUTCDATE()),
(2, 'librarian1', '<EMAIL>', '$2b$10$dummyHashForLibrarian123456', N'Thư viện', N'Một', 2, 1, 1, GETUTCDATE()),
(3, 'librarian2', '<EMAIL>', '$2b$10$dummyHashForLibrarian234567', N'Thư viện', N'Hai', 2, 1, 1, GETUTCDATE()),
(4, 'assistant1', '<EMAIL>', '$2b$10$dummyHashForAssistant123456', N'Trợ lý', N'Một', 3, 1, 1, GETUTCDATE()),
(5, 'assistant2', '<EMAIL>', '$2b$10$dummyHashForAssistant234567', N'Trợ lý', N'Hai', 3, 1, 1, GETUTCDATE());
SET IDENTITY_INSERT Users OFF;

-- =============================================
-- 4. THÊM MEMBERS
-- =============================================
SET IDENTITY_INSERT Members ON;
INSERT INTO Members (Id, FirstName, LastName, Email, Phone, Address, MembershipDate, Status, CreatedAt) VALUES
(1, N'Nguyễn Văn', N'An', '<EMAIL>', '0901234567', N'123 Đường ABC, Quận 1, TP.HCM', DATEADD(month, -6, GETUTCDATE()), 1, DATEADD(month, -6, GETUTCDATE())),
(2, N'Trần Thị', N'Bình', '<EMAIL>', '0902345678', N'456 Đường DEF, Quận 2, TP.HCM', DATEADD(month, -4, GETUTCDATE()), 1, DATEADD(month, -4, GETUTCDATE())),
(3, N'Lê Văn', N'Cường', '<EMAIL>', '0903456789', N'789 Đường GHI, Quận 3, TP.HCM', DATEADD(month, -8, GETUTCDATE()), 1, DATEADD(month, -8, GETUTCDATE())),
(4, N'Phạm Thị', N'Dung', '<EMAIL>', '0904567890', N'321 Đường JKL, Quận 4, TP.HCM', DATEADD(month, -2, GETUTCDATE()), 1, DATEADD(month, -2, GETUTCDATE())),
(5, N'Hoàng Văn', N'Em', '<EMAIL>', '0905678901', N'654 Đường MNO, Quận 5, TP.HCM', DATEADD(month, -10, GETUTCDATE()), 1, DATEADD(month, -10, GETUTCDATE())),
(6, N'Võ Thị', N'Phương', '<EMAIL>', '0906789012', N'987 Đường PQR, Quận 6, TP.HCM', DATEADD(month, -1, GETUTCDATE()), 1, DATEADD(month, -1, GETUTCDATE())),
(7, N'Đỗ Văn', N'Giang', '<EMAIL>', '0907890123', N'147 Đường STU, Quận 7, TP.HCM', DATEADD(month, -12, GETUTCDATE()), 1, DATEADD(month, -12, GETUTCDATE())),
(8, N'Bùi Thị', N'Hoa', '<EMAIL>', '0908901234', N'258 Đường VWX, Quận 8, TP.HCM', DATEADD(month, -3, GETUTCDATE()), 1, DATEADD(month, -3, GETUTCDATE())),
(9, N'Ngô Văn', N'Ích', '<EMAIL>', '0909012345', N'369 Đường YZ, Quận 9, TP.HCM', DATEADD(month, -7, GETUTCDATE()), 1, DATEADD(month, -7, GETUTCDATE())),
(10, N'Lý Thị', N'Kim', '<EMAIL>', '0900123456', N'741 Đường ABC, Quận 10, TP.HCM', DATEADD(month, -5, GETUTCDATE()), 1, DATEADD(month, -5, GETUTCDATE()));
SET IDENTITY_INSERT Members OFF;

-- =============================================
-- 5. THÊM BOOKS (Sử dụng StockQuantity thay vì AvailableQuantity)
-- =============================================
SET IDENTITY_INSERT Books ON;
INSERT INTO Books (Id, Title, Author, ISBN, Publisher, PublishedDate, CategoryId, Quantity, StockQuantity, Price, ImageUrl, Description, CreatedAt) VALUES
-- IT Books (CategoryId = 1)
(1, N'Clean Code: A Handbook of Agile Software Craftsmanship', N'Robert C. Martin', N'9780132350884', N'Prentice Hall', DATEFROMPARTS(2008, 1, 1), 1, 5, 2, 850000, N'https://m.media-amazon.com/images/I/41xShlnTZTL._SX376_BO1,204,203,200_.jpg', N'Hướng dẫn viết code sạch và dễ bảo trì cho các lập trình viên', GETUTCDATE()),
(2, N'Design Patterns: Elements of Reusable Object-Oriented Software', N'Erich Gamma, Richard Helm', N'9780201633610', N'Addison-Wesley', DATEFROMPARTS(1994, 1, 1), 1, 4, 1, 920000, N'https://m.media-amazon.com/images/I/81gtKoapHFL.jpg', N'Các mẫu thiết kế phần mềm hướng đối tượng kinh điển', GETUTCDATE()),
(3, N'JavaScript: The Good Parts', N'Douglas Crockford', N'9780596517748', N'O''Reilly Media', DATEFROMPARTS(2008, 1, 1), 1, 6, 3, 650000, N'https://m.media-amazon.com/images/I/5166ztxN-eL._SX381_BO1,204,203,200_.jpg', N'Những phần tốt nhất của ngôn ngữ JavaScript', GETUTCDATE()),
(4, N'C# in Depth', N'Jon Skeet', N'9781617294532', N'Manning Publications', DATEFROMPARTS(2019, 1, 1), 1, 4, 2, 780000, N'https://m.media-amazon.com/images/I/41JYiuLn0QL._SX397_BO1,204,203,200_.jpg', N'Hiểu sâu về ngôn ngữ lập trình C#', GETUTCDATE()),
(5, N'The Pragmatic Programmer', N'Andrew Hunt, David Thomas', N'9780135957059', N'Addison-Wesley', DATEFROMPARTS(2019, 1, 1), 1, 5, 2, 890000, N'https://m.media-amazon.com/images/I/41as+WafrFL._SX378_BO1,204,203,200_.jpg', N'Hướng dẫn thực tế cho lập trình viên chuyên nghiệp', GETUTCDATE()),

-- Vietnamese Literature (CategoryId = 2)
(6, N'Truyện Kiều', N'Nguyễn Du', N'9786041141179', N'NXB Kim Đồng', DATEFROMPARTS(1820, 1, 1), 2, 10, 6, 120000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Truyen+Kieu', N'Tác phẩm kinh điển của văn học Việt Nam', GETUTCDATE()),
(7, N'Số đỏ', N'Vũ Trọng Phụng', N'9786041033890', N'NXB Văn học', DATEFROMPARTS(1936, 1, 1), 2, 8, 4, 95000, N'https://images.placeholder.com/150x200/cccccc/969696?text=So+Do', N'Tiểu thuyết hiện thực phê phán nổi tiếng', GETUTCDATE()),
(8, N'Dế Mèn phiêu lưu ký', N'Tô Hoài', N'9786041000547', N'NXB Kim Đồng', DATEFROMPARTS(1941, 1, 1), 2, 7, 4, 85000, N'https://images.placeholder.com/150x200/cccccc/969696?text=De+Men', N'Truyện thiếu nhi kinh điển Việt Nam', GETUTCDATE()),
(9, N'Lão Hạc', N'Nam Cao', N'9786041001254', N'NXB Văn học', DATEFROMPARTS(1943, 1, 1), 2, 6, 3, 75000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Lao+Hac', N'Truyện ngắn nổi tiếng về số phận con người', GETUTCDATE()),

-- Foreign Literature (CategoryId = 3)
(10, N'1984', N'George Orwell', N'9780451524935', N'Signet Classic', DATEFROMPARTS(1949, 1, 1), 3, 6, 4, 180000, N'https://m.media-amazon.com/images/I/71kxa1-0mfL.jpg', N'Tiểu thuyết dystopia kinh điển về chủ nghĩa toàn trị', GETUTCDATE()),
(11, N'To Kill a Mockingbird', N'Harper Lee', N'9780061120084', N'Harper Perennial', DATEFROMPARTS(1960, 1, 1), 3, 5, 3, 195000, N'https://m.media-amazon.com/images/I/71FxgtFKcqL.jpg', N'Câu chuyện về công lý và thiên kiến ở miền Nam nước Mỹ', GETUTCDATE()),
(12, N'The Great Gatsby', N'F. Scott Fitzgerald', N'9780743273565', N'Scribner', DATEFROMPARTS(1925, 1, 1), 3, 4, 2, 165000, N'https://m.media-amazon.com/images/I/81QuEGw8VPL.jpg', N'Tiểu thuyết về giấc mơ Mỹ trong thời đại Jazz', GETUTCDATE()),
(13, N'Pride and Prejudice', N'Jane Austen', N'9780141439518', N'Penguin Classics', DATEFROMPARTS(1813, 1, 1), 3, 5, 3, 155000, N'https://m.media-amazon.com/images/I/71Q1tPupKjL.jpg', N'Câu chuyện tình yêu kinh điển của văn học Anh', GETUTCDATE()),

-- Science (CategoryId = 4)
(14, N'A Brief History of Time', N'Stephen Hawking', N'9780553380163', N'Bantam', DATEFROMPARTS(1988, 1, 1), 4, 4, 2, 220000, N'https://m.media-amazon.com/images/I/A1yl8SAXOgL.jpg', N'Khám phá vũ trụ và thời gian từ góc nhìn vật lý học', GETUTCDATE()),
(15, N'The Origin of Species', N'Charles Darwin', N'9780451529060', N'Signet Classic', DATEFROMPARTS(1859, 1, 1), 4, 3, 2, 185000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Origin+Species', N'Lý thuyết tiến hóa cách mạng của Darwin', GETUTCDATE()),
(16, N'Cosmos', N'Carl Sagan', N'9780345331359', N'Ballantine Books', DATEFROMPARTS(1980, 1, 1), 4, 4, 2, 240000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Cosmos', N'Hành trình khám phá vũ trụ qua khoa học', GETUTCDATE()),

-- Business (CategoryId = 5)
(17, N'Think and Grow Rich', N'Napoleon Hill', N'9781585424337', N'TarcherPerigee', DATEFROMPARTS(1937, 1, 1), 5, 5, 3, 175000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Think+Grow+Rich', N'Bí quyết thành công trong kinh doanh và cuộc sống', GETUTCDATE()),
(18, N'Good to Great', N'Jim Collins', N'9780066620992', N'HarperBusiness', DATEFROMPARTS(2001, 1, 1), 5, 4, 2, 195000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Good+Great', N'Nghiên cứu về các công ty chuyển từ tốt thành xuất sắc', GETUTCDATE()),
(19, N'The 7 Habits of Highly Effective People', N'Stephen R. Covey', N'9781982137274', N'Simon & Schuster', DATEFROMPARTS(1989, 1, 1), 5, 6, 4, 165000, N'https://images.placeholder.com/150x200/cccccc/969696?text=7+Habits', N'Phương pháp phát triển bản thân và lãnh đạo hiệu quả', GETUTCDATE()),

-- History (CategoryId = 6)
(20, N'Lịch sử Việt Nam', N'Trần Trọng Kim', N'9786041080259', N'NXB Tổng hợp TP.HCM', DATEFROMPARTS(1920, 1, 1), 6, 5, 3, 145000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Lich+Su+VN', N'Lịch sử Việt Nam từ thời cổ đại đến cận đại', GETUTCDATE()),
(21, N'Sapiens: A Brief History of Humankind', N'Yuval Noah Harari', N'9780062316097', N'Harper', DATEFROMPARTS(2014, 1, 1), 6, 4, 2, 280000, N'https://m.media-amazon.com/images/I/713jIoMO3UL.jpg', N'Lịch sử loài người từ thời tiền sử đến hiện đại', GETUTCDATE()),
(22, N'The Guns of August', N'Barbara Tuchman', N'9780345476098', N'Ballantine Books', DATEFROMPARTS(1962, 1, 1), 6, 3, 2, 210000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Guns+August', N'Tháng đầu tiên của Thế chiến thứ nhất', GETUTCDATE());
SET IDENTITY_INSERT Books OFF;

-- =============================================
-- 6. THÊM BOOKSHELVES
-- =============================================
SET IDENTITY_INSERT Bookshelves ON;
INSERT INTO Bookshelves (Id, Name, Description, ZoneId, CategoryId, Capacity, CurrentCount, Status, CreatedAt) VALUES
-- Kệ IT (Zone 3, Category 1)
(1, N'Kệ IT-01', N'Kệ sách lập trình cơ bản', 3, 1, 100, 0, 'Active', GETUTCDATE()),
(2, N'Kệ IT-02', N'Kệ sách thiết kế phần mềm', 3, 1, 100, 0, 'Active', GETUTCDATE()),

-- Kệ Văn học Việt Nam (Zone 4, Category 2)
(3, N'Kệ VH-01', N'Kệ văn học cổ điển Việt Nam', 4, 2, 100, 0, 'Active', GETUTCDATE()),
(4, N'Kệ VH-02', N'Kệ văn học hiện đại Việt Nam', 4, 2, 100, 0, 'Active', GETUTCDATE()),

-- Kệ Văn học nước ngoài (Zone 4, Category 3)
(5, N'Kệ VH-03', N'Kệ văn học Anh Mỹ', 4, 3, 100, 0, 'Active', GETUTCDATE()),
(6, N'Kệ VH-04', N'Kệ văn học châu Âu', 4, 3, 100, 0, 'Active', GETUTCDATE()),

-- Kệ Khoa học (Zone 5, Category 4)
(7, N'Kệ KH-01', N'Kệ khoa học tự nhiên', 5, 4, 100, 0, 'Active', GETUTCDATE()),
(8, N'Kệ KH-02', N'Kệ vật lý thiên văn', 5, 4, 100, 0, 'Active', GETUTCDATE()),

-- Kệ Kinh doanh (Zone 1, Category 5)
(9, N'Kệ KD-01', N'Kệ quản lý kinh doanh', 1, 5, 100, 0, 'Active', GETUTCDATE()),
(10, N'Kệ KD-02', N'Kệ phát triển bản thân', 1, 5, 100, 0, 'Active', GETUTCDATE()),

-- Kệ Lịch sử (Zone 2, Category 6)
(11, N'Kệ LS-01', N'Kệ lịch sử Việt Nam', 2, 6, 100, 0, 'Active', GETUTCDATE()),
(12, N'Kệ LS-02', N'Kệ lịch sử thế giới', 2, 6, 100, 0, 'Active', GETUTCDATE());
SET IDENTITY_INSERT Bookshelves OFF;

-- =============================================
-- 7. THÊM SHELF SLOTS (Chỉ có BookshelfId, SlotCode, Size)
-- =============================================
SET IDENTITY_INSERT ShelfSlots ON;
DECLARE @ShelfId INT = 1;
DECLARE @SlotId INT = 1;

WHILE @ShelfId <= 12
BEGIN
    DECLARE @Row CHAR(1) = 'A';
    WHILE @Row <= 'D'
    BEGIN
        DECLARE @Col INT = 1;
        WHILE @Col <= 5
        BEGIN
            INSERT INTO ShelfSlots (Id, BookshelfId, SlotCode, Size, CreatedAt)
            VALUES (@SlotId, @ShelfId, @Row + CAST(@Col AS CHAR(1)), 5, GETUTCDATE());
            
            SET @SlotId = @SlotId + 1;
            SET @Col = @Col + 1;
        END
        SET @Row = CHAR(ASCII(@Row) + 1);
    END
    SET @ShelfId = @ShelfId + 1;
END
SET IDENTITY_INSERT ShelfSlots OFF;

-- =============================================
-- 8. THÊM SHELF SLOT BOOKS (Assign books to slots)
-- =============================================
SET IDENTITY_INSERT ShelfSlotBooks ON;
INSERT INTO ShelfSlotBooks (Id, ShelfSlotId, BookId, Quantity, BorrowQuantity, CreatedAt) VALUES
-- IT books in IT shelves (ShelfSlotId 1-40 thuộc kệ IT-01 và IT-02)
(1, 1, 1, 2, 1, GETUTCDATE()),  -- Clean Code: 2 cuốn, 1 đã mượn
(2, 2, 1, 1, 0, GETUTCDATE()),  -- Clean Code: 1 cuốn thêm
(3, 3, 2, 3, 1, GETUTCDATE()),  -- Design Patterns: 3 cuốn, 1 đã mượn
(4, 21, 3, 3, 1, GETUTCDATE()), -- JavaScript: 3 cuốn trong kệ IT-02
(5, 22, 4, 2, 1, GETUTCDATE()), -- C# in Depth: 2 cuốn, 1 đã mượn
(6, 23, 5, 3, 1, GETUTCDATE()), -- Pragmatic Programmer: 3 cuốn, 1 đã mượn

-- Vietnamese Literature books (ShelfSlotId 41-80 thuộc kệ VH-01 và VH-02)
(7, 41, 6, 4, 1, GETUTCDATE()), -- Truyện Kiều: 4 cuốn, 1 đã mượn
(8, 42, 7, 4, 1, GETUTCDATE()), -- Số đỏ: 4 cuốn, 1 đã mượn
(9, 61, 7, 3, 0, GETUTCDATE()), -- Số đỏ: 3 cuốn thêm trong kệ VH-02
(10, 62, 8, 3, 1, GETUTCDATE()), -- Dế Mèn: 3 cuốn, 1 đã mượn
(11, 63, 9, 3, 1, GETUTCDATE()), -- Lão Hạc: 3 cuốn, 1 đã mượn

-- Foreign Literature books (ShelfSlotId 81-120 thuộc kệ VH-03 và VH-04)
(12, 81, 10, 2, 0, GETUTCDATE()), -- 1984: 2 cuốn
(13, 82, 11, 2, 0, GETUTCDATE()), -- To Kill a Mockingbird: 2 cuốn
(14, 101, 12, 2, 0, GETUTCDATE()), -- The Great Gatsby: 2 cuốn
(15, 102, 13, 2, 0, GETUTCDATE()), -- Pride and Prejudice: 2 cuốn

-- Science books (ShelfSlotId 121-160 thuộc kệ KH-01 và KH-02)
(16, 121, 14, 2, 0, GETUTCDATE()), -- A Brief History of Time: 2 cuốn
(17, 122, 15, 1, 0, GETUTCDATE()), -- The Origin of Species: 1 cuốn
(18, 141, 16, 2, 0, GETUTCDATE()), -- Cosmos: 2 cuốn

-- Business books (ShelfSlotId 161-200 thuộc kệ KD-01 và KD-02)
(19, 161, 17, 2, 0, GETUTCDATE()), -- Think and Grow Rich: 2 cuốn
(20, 162, 18, 2, 0, GETUTCDATE()), -- Good to Great: 2 cuốn
(21, 181, 19, 2, 0, GETUTCDATE()), -- 7 Habits: 2 cuốn

-- History books (ShelfSlotId 201-240 thuộc kệ LS-01 và LS-02)
(22, 201, 20, 2, 0, GETUTCDATE()), -- Lịch sử Việt Nam: 2 cuốn
(23, 221, 21, 2, 0, GETUTCDATE()), -- Sapiens: 2 cuốn
(24, 222, 22, 1, 0, GETUTCDATE()); -- The Guns of August: 1 cuốn
SET IDENTITY_INSERT ShelfSlotBooks OFF;

-- =============================================
-- 9. THÊM BORROW RECORDS
-- =============================================
SET IDENTITY_INSERT BorrowRecords ON;
INSERT INTO BorrowRecords (Id, BookId, MemberId, BorrowDate, DueDate, ReturnDate, Status, CreatedAt) VALUES
-- Sách đang được mượn (Status = 1) - 6 bản ghi
(1, 1, 1, DATEADD(day, -5, GETUTCDATE()), DATEADD(day, 9, GETUTCDATE()), NULL, 1, DATEADD(day, -5, GETUTCDATE())),
(2, 2, 2, DATEADD(day, -3, GETUTCDATE()), DATEADD(day, 11, GETUTCDATE()), NULL, 1, DATEADD(day, -3, GETUTCDATE())),
(3, 3, 3, DATEADD(day, -7, GETUTCDATE()), DATEADD(day, 7, GETUTCDATE()), NULL, 1, DATEADD(day, -7, GETUTCDATE())),
(4, 4, 4, DATEADD(day, -2, GETUTCDATE()), DATEADD(day, 12, GETUTCDATE()), NULL, 1, DATEADD(day, -2, GETUTCDATE())),
(5, 5, 5, DATEADD(day, -1, GETUTCDATE()), DATEADD(day, 13, GETUTCDATE()), NULL, 1, DATEADD(day, -1, GETUTCDATE())),
(6, 6, 6, DATEADD(day, -4, GETUTCDATE()), DATEADD(day, 10, GETUTCDATE()), NULL, 1, DATEADD(day, -4, GETUTCDATE())),

-- Sách quá hạn (Status = 3) - 3 bản ghi
(7, 7, 7, DATEADD(day, -20, GETUTCDATE()), DATEADD(day, -6, GETUTCDATE()), NULL, 3, DATEADD(day, -20, GETUTCDATE())),
(8, 8, 8, DATEADD(day, -18, GETUTCDATE()), DATEADD(day, -4, GETUTCDATE()), NULL, 3, DATEADD(day, -18, GETUTCDATE())),
(9, 9, 9, DATEADD(day, -25, GETUTCDATE()), DATEADD(day, -11, GETUTCDATE()), NULL, 3, DATEADD(day, -25, GETUTCDATE())),

-- Sách đã trả (Status = 2) - 3 bản ghi
(10, 10, 10, DATEADD(day, -25, GETUTCDATE()), DATEADD(day, -11, GETUTCDATE()), DATEADD(day, -12, GETUTCDATE()), 2, DATEADD(day, -25, GETUTCDATE())),
(11, 11, 1, DATEADD(day, -30, GETUTCDATE()), DATEADD(day, -16, GETUTCDATE()), DATEADD(day, -17, GETUTCDATE()), 2, DATEADD(day, -30, GETUTCDATE())),
(12, 12, 2, DATEADD(day, -35, GETUTCDATE()), DATEADD(day, -21, GETUTCDATE()), DATEADD(day, -22, GETUTCDATE()), 2, DATEADD(day, -35, GETUTCDATE()));
SET IDENTITY_INSERT BorrowRecords OFF;

-- =============================================
-- 10. CẬP NHẬT CURRENT COUNT CHO BOOKSHELVES
-- =============================================
UPDATE Bookshelves 
SET CurrentCount = (
    SELECT ISNULL(SUM(ssb.Quantity), 0) 
    FROM ShelfSlots ss
    JOIN ShelfSlotBooks ssb ON ss.Id = ssb.ShelfSlotId
    WHERE ss.BookshelfId = Bookshelves.Id
);

PRINT '=================================================';
PRINT 'COMPLETE SAMPLE DATA INSERTED SUCCESSFULLY!';
PRINT '=================================================';
PRINT '';
PRINT 'LOGIN ACCOUNTS:';
PRINT '- Admin: <EMAIL> / Admin123!';
PRINT '- Librarian 1: <EMAIL> / Librarian123!';
PRINT '- Librarian 2: <EMAIL> / Librarian123!';
PRINT '- Assistant 1: <EMAIL> / Assistant123!';
PRINT '- Assistant 2: <EMAIL> / Assistant123!';
PRINT '';
PRINT 'DATA SUMMARY:';
PRINT '- Categories: 6 (IT, Văn học VN, Văn học NN, Khoa học, Kinh doanh, Lịch sử)';
PRINT '- Zones: 5 (Tầng 1A, 1B, 2-IT, 2-Văn học, 3-Khoa học)';
PRINT '- Bookshelves: 12 (2 kệ mỗi category)';
PRINT '- Shelf Slots: 240 (20 slots per shelf: A1-D5)';
PRINT '- Shelf Slot Books: 24 (books assigned to specific slots)';
PRINT '- Users: 5 (1 Admin, 2 Librarians, 2 Assistants)';
PRINT '- Members: 10 (Active library members)';
PRINT '- Books: 22 (with StockQuantity for storage)';
PRINT '- Borrow Records: 12 (6 active, 3 overdue, 3 returned)';
PRINT '';
PRINT 'SHELF ORGANIZATION:';
PRINT '- Kệ IT-01, IT-02: Sách công nghệ thông tin';
PRINT '- Kệ VH-01, VH-02: Sách văn học Việt Nam';
PRINT '- Kệ VH-03, VH-04: Sách văn học nước ngoài';
PRINT '- Kệ KH-01, KH-02: Sách khoa học';
PRINT '- Kệ KD-01, KD-02: Sách kinh doanh';
PRINT '- Kệ LS-01, LS-02: Sách lịch sử';
PRINT '';
PRINT 'CORRECT STRUCTURE:';
PRINT '- Books have StockQuantity (not AvailableQuantity)';
PRINT '- ShelfSlots only have BookshelfId, SlotCode, Size';
PRINT '- ShelfSlotBooks link slots to books with Quantity and BorrowQuantity';
PRINT 'Ready for testing with realistic data!'; 