import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

export interface BreadcrumbItem {
  label: string;
  url?: string;
  icon?: string;
  active?: boolean;
}

@Component({
  selector: 'app-breadcrumb',
  standalone: true,
  imports: [CommonModule, RouterModule, MatIconModule, MatButtonModule],
  template: `
    <nav class="breadcrumb-container" aria-label="Breadcrumb">
      <ol class="breadcrumb-list">
        <li class="breadcrumb-item home-item">
          <a routerLink="/dashboard" class="breadcrumb-link">
            <mat-icon>home</mat-icon>
            <span class="home-text">Trang chủ</span>
          </a>
        </li>
        
        <li 
          *ngFor="let item of items; let last = last" 
          class="breadcrumb-item"
          [class.active]="last || item.active">
          
          <mat-icon class="separator">chevron_right</mat-icon>
          
          <a 
            *ngIf="item.url && !last && !item.active" 
            [routerLink]="item.url" 
            class="breadcrumb-link">
            <mat-icon *ngIf="item.icon">{{ item.icon }}</mat-icon>
            <span>{{ item.label }}</span>
          </a>
          
          <span 
            *ngIf="!item.url || last || item.active" 
            class="breadcrumb-current">
            <mat-icon *ngIf="item.icon">{{ item.icon }}</mat-icon>
            <span>{{ item.label }}</span>
          </span>
        </li>
      </ol>
    </nav>
  `,
  styles: [`
    .breadcrumb-container {
      padding: var(--spacing-md) var(--spacing-xl);
      background: var(--color-surface);
      border-bottom: 1px solid var(--color-outline);
    }

    .breadcrumb-list {
      display: flex;
      align-items: center;
      list-style: none;
      margin: 0;
      padding: 0;
      flex-wrap: wrap;
      gap: var(--spacing-xs);
    }

    .breadcrumb-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);

      &.home-item {
        .breadcrumb-link {
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
          padding: var(--spacing-xs) var(--spacing-sm);
          border-radius: var(--radius-small);
          transition: all var(--transition-fast);
          
          &:hover {
            background-color: var(--color-surface-variant);
          }

          .home-text {
            @media (max-width: 768px) {
              display: none;
            }
          }
        }
      }

      &.active {
        .breadcrumb-current {
          color: var(--color-primary);
          font-weight: var(--font-weight-semibold);
        }
      }
    }

    .breadcrumb-link {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      color: var(--color-on-surface);
      text-decoration: none;
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-small);
      transition: all var(--transition-fast);
      font-size: var(--font-size-sm);

      &:hover {
        background-color: var(--color-surface-variant);
        color: var(--color-primary);
      }

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }

    .breadcrumb-current {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      color: var(--color-on-surface);
      opacity: 0.8;
      font-size: var(--font-size-sm);
      padding: var(--spacing-xs) var(--spacing-sm);

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }

    .separator {
      color: var(--color-on-surface);
      opacity: 0.5;
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    @media (max-width: 768px) {
      .breadcrumb-container {
        padding: var(--spacing-sm) var(--spacing-md);
      }

      .breadcrumb-list {
        gap: var(--spacing-xs);
      }

      .breadcrumb-link,
      .breadcrumb-current {
        font-size: var(--font-size-xs);
        padding: var(--spacing-xs);
      }

      .separator {
        font-size: 14px;
        width: 14px;
        height: 14px;
      }
    }
  `]
})
export class BreadcrumbComponent {
  @Input() items: BreadcrumbItem[] = [];
}
