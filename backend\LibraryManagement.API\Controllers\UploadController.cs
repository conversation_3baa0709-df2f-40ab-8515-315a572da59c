using Microsoft.AspNetCore.Mvc;
using LibraryManagement.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using System.Linq;

namespace LibraryManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[AllowAnonymous] // Allow anonymous access for all endpoints
public class UploadController : ControllerBase
{
    private readonly ICloudinaryService _cloudinaryService;
    private readonly ILogger<UploadController> _logger;

    public UploadController(ICloudinaryService cloudinaryService, ILogger<UploadController> logger)
    {
        _cloudinaryService = cloudinaryService;
        _logger = logger;
    }

    [HttpPost("image")]
    public async Task<IActionResult> UploadImage([FromForm] IFormFile image)
    {
        try
    {
        if (image == null || image.Length == 0)
                return BadRequest(new { message = "Không có ảnh được gửi lên" });

            // Validate file type
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
            var fileExtension = Path.GetExtension(image.FileName).ToLowerInvariant();
            
            if (!allowedExtensions.Contains(fileExtension))
                return BadRequest(new { message = "Chỉ chấp nhận file ảnh (.jpg, .jpeg, .png, .gif, .webp)" });

            // Validate file size (max 5MB)
            const int maxFileSize = 5 * 1024 * 1024; // 5MB
            if (image.Length > maxFileSize)
                return BadRequest(new { message = "Kích thước file không được vượt quá 5MB" });

            // Upload to Cloudinary
            using var stream = image.OpenReadStream();
            var imageUrl = await _cloudinaryService.UploadImageAsync(stream, image.FileName);

            _logger.LogInformation("Image uploaded successfully to Cloudinary: {ImageUrl}", imageUrl);

            return Ok(new { imageUrl });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading image");
            return StatusCode(500, new { message = "Lỗi khi upload ảnh. Vui lòng thử lại sau." });
        }
    }

    [HttpDelete("image")]
    public async Task<IActionResult> DeleteImage([FromQuery] string imageUrl)
    {
        try
        {
            if (string.IsNullOrEmpty(imageUrl))
                return BadRequest(new { message = "URL ảnh là bắt buộc" });

            var publicId = _cloudinaryService.GetPublicIdFromUrl(imageUrl);
            if (string.IsNullOrEmpty(publicId))
                return BadRequest(new { message = "Không thể xác định ID ảnh từ URL" });

            var deleted = await _cloudinaryService.DeleteImageAsync(publicId);
            
            if (deleted)
            {
                _logger.LogInformation("Image deleted successfully from Cloudinary: {PublicId}", publicId);
                return Ok(new { message = "Xóa ảnh thành công" });
            }
            else
            {
                return BadRequest(new { message = "Không thể xóa ảnh" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting image");
            return StatusCode(500, new { message = "Lỗi khi xóa ảnh. Vui lòng thử lại sau." });
        }
    }
}
