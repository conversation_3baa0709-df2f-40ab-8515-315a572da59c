using LibraryManagement.Core.Enums;

namespace LibraryManagement.Application.DTOs;

public class DashboardStatsDto
{
    public int TotalBooks { get; set; }
    public int TotalMembers { get; set; }
    public int TotalBorrows { get; set; }
    public int ActiveBorrows { get; set; }
    public int OverdueBooks { get; set; }
    public decimal TotalFines { get; set; }
    public int TotalCategories { get; set; }
    public int TotalUsers { get; set; }
}

public class PopularBookDto
{
    public int BookId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public int BorrowCount { get; set; }
    public string? ImageUrl { get; set; }
    public string CategoryName { get; set; } = string.Empty;
}

public class ActiveMemberDto
{
    public int MemberId { get; set; }
    public string FullName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public int BorrowCount { get; set; }
    public MemberStatus Status { get; set; }
}

public class CategoryStatsDto
{
    public int CategoryId { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public int BookCount { get; set; }
    public int BorrowCount { get; set; }
}

public class MonthlyStatsDto
{
    public int Year { get; set; }
    public int Month { get; set; }
    public string MonthName { get; set; } = string.Empty;
    public int NewBorrows { get; set; }
    public int Returns { get; set; }
    public int NewMembers { get; set; }
    public decimal TotalFines { get; set; }
}

public class DailyStatsDto
{
    public DateTime Date { get; set; }
    public int NewBorrows { get; set; }
    public int Returns { get; set; }
    public decimal TotalFines { get; set; }
}

public class ReportRequestDto
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public ReportType ReportType { get; set; }
    public int? CategoryId { get; set; }
    public int? MemberId { get; set; }
    public BorrowStatus? BorrowStatus { get; set; }
}

public enum ReportType
{
    Daily = 1,
    Monthly = 2,
    Yearly = 3,
    CategoryWise = 4,
    MemberActivity = 5,
    FineCollection = 6,
    BookStatus = 7
}