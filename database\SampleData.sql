-- Library Management Sample Data
-- <PERSON><PERSON><PERSON> để thêm dữ liệu mẫu cho ứng dụng quản lý thư viện

USE LibraryManagementDb;
GO

-- <PERSON><PERSON>a dữ liệu cũ (theo thứ tự để tránh FK constraint)
DELETE FROM BorrowRecords;
DELETE FROM Books;
DELETE FROM Members;
DELETE FROM Users;
DELETE FROM Categories;

-- Reset Identity seeds
DBCC CHECKIDENT ('BorrowRecords', RESEED, 0);
DBCC CHECKIDENT ('Books', RESEED, 0);
DBCC CHECKIDENT ('Members', RESEED, 0);
DBCC CHECKIDENT ('Users', RESEED, 0);
DBCC CHECKIDENT ('Categories', RESEED, 0);

PRINT 'Deleted all existing data and reset identity seeds.';

-- Thêm Categories
SET IDENTITY_INSERT Categories ON;
INSERT INTO Categories (Id, Name, Description, CreatedAt) VALUES
(1, N'Công nghệ thông tin', N'Sách về lập trình, ph<PERSON><PERSON> mềm và công nghệ', GETUTCDATE()),
(2, N'<PERSON>ă<PERSON> học Việt Nam', N'Tác phẩm văn học của các tác giả Việt Nam', GETUTCDATE()),
(3, N'Văn học nước ngoài', N'Tác phẩm văn học được dịch từ nước ngoài', GETUTCDATE()),
(4, N'Khoa học', N'Sách về khoa học tự nhiên và ứng dụng', GETUTCDATE()),
(5, N'Kinh doanh', N'Sách về quản lý, kinh doanh và tài chính', GETUTCDATE()),
(6, N'Lịch sử', N'Sách về lịch sử Việt Nam và thế giới', GETUTCDATE());
SET IDENTITY_INSERT Categories OFF;

-- Thêm Users (5 tài khoản)
SET IDENTITY_INSERT Users ON;
INSERT INTO Users (Id, Username, Email, PasswordHash, FirstName, LastName, Role, IsActive, EmailVerified, CreatedAt) VALUES
(1, 'admin', '<EMAIL>', '$2b$10$dummyHashForAdmin123456789', N'Admin', N'System', 1, 1, 1, GETUTCDATE()),
(2, 'librarian1', '<EMAIL>', '$2b$10$dummyHashForLibrarian123456', N'Thư viện', N'Một', 2, 1, 1, GETUTCDATE()),
(3, 'librarian2', '<EMAIL>', '$2b$10$dummyHashForLibrarian234567', N'Thư viện', N'Hai', 2, 1, 1, GETUTCDATE()),
(4, 'assistant1', '<EMAIL>', '$2b$10$dummyHashForAssistant123456', N'Trợ lý', N'Một', 3, 1, 1, GETUTCDATE()),
(5, 'assistant2', '<EMAIL>', '$2b$10$dummyHashForAssistant234567', N'Trợ lý', N'Hai', 3, 1, 1, GETUTCDATE());
SET IDENTITY_INSERT Users OFF;

-- Thêm Members (10 thành viên)
SET IDENTITY_INSERT Members ON;
INSERT INTO Members (Id, FirstName, LastName, Email, Phone, Address, MembershipDate, Status, CreatedAt) VALUES
(1, N'Nguyễn Văn', N'An', '<EMAIL>', '0901234567', N'123 Đường ABC, Quận 1, TP.HCM', DATEADD(month, -6, GETUTCDATE()), 1, DATEADD(month, -6, GETUTCDATE())),
(2, N'Trần Thị', N'Bình', '<EMAIL>', '0902345678', N'456 Đường DEF, Quận 2, TP.HCM', DATEADD(month, -4, GETUTCDATE()), 1, DATEADD(month, -4, GETUTCDATE())),
(3, N'Lê Văn', N'Cường', '<EMAIL>', '0903456789', N'789 Đường GHI, Quận 3, TP.HCM', DATEADD(month, -8, GETUTCDATE()), 1, DATEADD(month, -8, GETUTCDATE())),
(4, N'Phạm Thị', N'Dung', '<EMAIL>', '0904567890', N'321 Đường JKL, Quận 4, TP.HCM', DATEADD(month, -2, GETUTCDATE()), 1, DATEADD(month, -2, GETUTCDATE())),
(5, N'Hoàng Văn', N'Em', '<EMAIL>', '0905678901', N'654 Đường MNO, Quận 5, TP.HCM', DATEADD(month, -10, GETUTCDATE()), 1, DATEADD(month, -10, GETUTCDATE())),
(6, N'Võ Thị', N'Phương', '<EMAIL>', '0906789012', N'987 Đường PQR, Quận 6, TP.HCM', DATEADD(month, -1, GETUTCDATE()), 1, DATEADD(month, -1, GETUTCDATE())),
(7, N'Đỗ Văn', N'Giang', '<EMAIL>', '0907890123', N'147 Đường STU, Quận 7, TP.HCM', DATEADD(month, -12, GETUTCDATE()), 1, DATEADD(month, -12, GETUTCDATE())),
(8, N'Bùi Thị', N'Hoa', '<EMAIL>', '0908901234', N'258 Đường VWX, Quận 8, TP.HCM', DATEADD(month, -3, GETUTCDATE()), 1, DATEADD(month, -3, GETUTCDATE())),
(9, N'Ngô Văn', N'Ích', '<EMAIL>', '0909012345', N'369 Đường YZ, Quận 9, TP.HCM', DATEADD(month, -7, GETUTCDATE()), 1, DATEADD(month, -7, GETUTCDATE())),
(10, N'Lý Thị', N'Kim', '<EMAIL>', '0900123456', N'741 Đường ABC, Quận 10, TP.HCM', DATEADD(month, -5, GETUTCDATE()), 1, DATEADD(month, -5, GETUTCDATE()));
SET IDENTITY_INSERT Members OFF;

-- Thêm Books (22 cuốn sách)
SET IDENTITY_INSERT Books ON;
INSERT INTO Books (Id, Title, Author, ISBN, Publisher, PublishedDate, CategoryId, Quantity, Price, ImageUrl, Description, CreatedAt) VALUES
-- IT Books (CategoryId = 1)
(1, N'Clean Code: A Handbook of Agile Software Craftsmanship', N'Robert C. Martin', N'9780132350884', N'Prentice Hall', DATEFROMPARTS(2008, 1, 1), 1, 5, 850000, N'https://m.media-amazon.com/images/I/41xShlnTZTL._SX376_BO1,204,203,200_.jpg', N'Hướng dẫn viết code sạch và dễ bảo trì cho các lập trình viên', GETUTCDATE()),
(2, N'Design Patterns: Elements of Reusable Object-Oriented Software', N'Erich Gamma, Richard Helm', N'9780201633610', N'Addison-Wesley', DATEFROMPARTS(1994, 1, 1), 1, 3, 920000, N'https://m.media-amazon.com/images/I/81gtKoapHFL.jpg', N'Các mẫu thiết kế phần mềm hướng đối tượng kinh điển', GETUTCDATE()),
(3, N'JavaScript: The Good Parts', N'Douglas Crockford', N'9780596517748', N'O''Reilly Media', DATEFROMPARTS(2008, 1, 1), 1, 4, 650000, N'https://m.media-amazon.com/images/I/5166ztxN-eL._SX381_BO1,204,203,200_.jpg', N'Những phần tốt nhất của ngôn ngữ JavaScript', GETUTCDATE()),
(4, N'C# in Depth', N'Jon Skeet', N'9781617294532', N'Manning Publications', DATEFROMPARTS(2019, 1, 1), 1, 3, 780000, N'https://m.media-amazon.com/images/I/41JYiuLn0QL._SX397_BO1,204,203,200_.jpg', N'Hiểu sâu về ngôn ngữ lập trình C#', GETUTCDATE()),
(5, N'The Pragmatic Programmer', N'Andrew Hunt, David Thomas', N'9780135957059', N'Addison-Wesley', DATEFROMPARTS(2019, 1, 1), 1, 4, 890000, N'https://m.media-amazon.com/images/I/41as+WafrFL._SX378_BO1,204,203,200_.jpg', N'Hướng dẫn thực tế cho lập trình viên chuyên nghiệp', GETUTCDATE()),

-- Vietnamese Literature (CategoryId = 2)
(6, N'Truyện Kiều', N'Nguyễn Du', N'9786041141179', N'NXB Kim Đồng', DATEFROMPARTS(1820, 1, 1), 2, 10, 120000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Truyen+Kieu', N'Tác phẩm kinh điển của văn học Việt Nam', GETUTCDATE()),
(7, N'Số đỏ', N'Vũ Trọng Phụng', N'9786041033890', N'NXB Văn học', DATEFROMPARTS(1936, 1, 1), 2, 6, 95000, N'https://images.placeholder.com/150x200/cccccc/969696?text=So+Do', N'Tiểu thuyết hiện thực phê phán nổi tiếng', GETUTCDATE()),
(8, N'Dế Mèn phiêu lưu ký', N'Tô Hoài', N'9786041000547', N'NXB Kim Đồng', DATEFROMPARTS(1941, 1, 1), 2, 8, 85000, N'https://images.placeholder.com/150x200/cccccc/969696?text=De+Men', N'Truyện thiếu nhi kinh điển Việt Nam', GETUTCDATE()),
(9, N'Lão Hạc', N'Nam Cao', N'9786041001254', N'NXB Văn học', DATEFROMPARTS(1943, 1, 1), 2, 7, 75000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Lao+Hac', N'Truyện ngắn nổi tiếng về số phận con người', GETUTCDATE()),

-- Foreign Literature (CategoryId = 3)
(10, N'1984', N'George Orwell', N'9780451524935', N'Signet Classic', DATEFROMPARTS(1949, 1, 1), 3, 6, 180000, N'https://m.media-amazon.com/images/I/71kxa1-0mfL.jpg', N'Tiểu thuyết dystopia kinh điển về chủ nghĩa toàn trị', GETUTCDATE()),
(11, N'To Kill a Mockingbird', N'Harper Lee', N'9780061120084', N'Harper Perennial', DATEFROMPARTS(1960, 1, 1), 3, 5, 195000, N'https://m.media-amazon.com/images/I/71FxgtFKcqL.jpg', N'Câu chuyện về công lý và thiên kiến ở miền Nam nước Mỹ', GETUTCDATE()),
(12, N'The Great Gatsby', N'F. Scott Fitzgerald', N'9780743273565', N'Scribner', DATEFROMPARTS(1925, 1, 1), 3, 4, 165000, N'https://m.media-amazon.com/images/I/81QuEGw8VPL.jpg', N'Tiểu thuyết về giấc mơ Mỹ trong thời đại Jazz', GETUTCDATE()),
(13, N'Pride and Prejudice', N'Jane Austen', N'9780141439518', N'Penguin Classics', DATEFROMPARTS(1813, 1, 1), 3, 5, 155000, N'https://m.media-amazon.com/images/I/71Q1tPupKjL.jpg', N'Câu chuyện tình yêu kinh điển của văn học Anh', GETUTCDATE()),

-- Science (CategoryId = 4)
(14, N'A Brief History of Time', N'Stephen Hawking', N'9780553380163', N'Bantam', DATEFROMPARTS(1988, 1, 1), 4, 4, 220000, N'https://m.media-amazon.com/images/I/A1yl8SAXOgL.jpg', N'Khám phá vũ trụ và thời gian từ góc nhìn vật lý học', GETUTCDATE()),
(15, N'The Origin of Species', N'Charles Darwin', N'9780451529060', N'Signet Classic', DATEFROMPARTS(1859, 1, 1), 4, 3, 185000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Origin+Species', N'Lý thuyết tiến hóa cách mạng của Darwin', GETUTCDATE()),
(16, N'Cosmos', N'Carl Sagan', N'9780345331359', N'Ballantine Books', DATEFROMPARTS(1980, 1, 1), 4, 4, 240000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Cosmos', N'Hành trình khám phá vũ trụ qua khoa học', GETUTCDATE()),

-- Business (CategoryId = 5)
(17, N'Think and Grow Rich', N'Napoleon Hill', N'9781585424337', N'TarcherPerigee', DATEFROMPARTS(1937, 1, 1), 5, 5, 175000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Think+Grow+Rich', N'Bí quyết thành công trong kinh doanh và cuộc sống', GETUTCDATE()),
(18, N'Good to Great', N'Jim Collins', N'9780066620992', N'HarperBusiness', DATEFROMPARTS(2001, 1, 1), 5, 4, 195000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Good+Great', N'Nghiên cứu về các công ty chuyển từ tốt thành xuất sắc', GETUTCDATE()),
(19, N'The 7 Habits of Highly Effective People', N'Stephen R. Covey', N'9781982137274', N'Simon & Schuster', DATEFROMPARTS(1989, 1, 1), 5, 6, 165000, N'https://images.placeholder.com/150x200/cccccc/969696?text=7+Habits', N'Phương pháp phát triển bản thân và lãnh đạo hiệu quả', GETUTCDATE()),

-- History (CategoryId = 6)
(20, N'Lịch sử Việt Nam', N'Trần Trọng Kim', N'9786041080259', N'NXB Tổng hợp TP.HCM', DATEFROMPARTS(1920, 1, 1), 6, 5, 145000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Lich+Su+VN', N'Lịch sử Việt Nam từ thời cổ đại đến cận đại', GETUTCDATE()),
(21, N'Sapiens: A Brief History of Humankind', N'Yuval Noah Harari', N'9780062316097', N'Harper', DATEFROMPARTS(2014, 1, 1), 6, 4, 280000, N'https://m.media-amazon.com/images/I/713jIoMO3UL.jpg', N'Lịch sử loài người từ thời tiền sử đến hiện đại', GETUTCDATE()),
(22, N'The Guns of August', N'Barbara Tuchman', N'9780345476098', N'Ballantine Books', DATEFROMPARTS(1962, 1, 1), 6, 3, 210000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Guns+August', N'Tháng đầu tiên của Thế chiến thứ nhất', GETUTCDATE());
SET IDENTITY_INSERT Books OFF;

-- Thêm Borrow Records (12 bản ghi)
SET IDENTITY_INSERT BorrowRecords ON;
INSERT INTO BorrowRecords (Id, BookId, MemberId, BorrowDate, DueDate, ReturnDate, Status, CreatedAt) VALUES
-- Sách đang được mượn (Status = 1) - 6 bản ghi
(1, 1, 1, DATEADD(day, -5, GETUTCDATE()), DATEADD(day, 9, GETUTCDATE()), NULL, 1, DATEADD(day, -5, GETUTCDATE())),
(2, 2, 2, DATEADD(day, -3, GETUTCDATE()), DATEADD(day, 11, GETUTCDATE()), NULL, 1, DATEADD(day, -3, GETUTCDATE())),
(3, 3, 3, DATEADD(day, -7, GETUTCDATE()), DATEADD(day, 7, GETUTCDATE()), NULL, 1, DATEADD(day, -7, GETUTCDATE())),
(4, 4, 4, DATEADD(day, -2, GETUTCDATE()), DATEADD(day, 12, GETUTCDATE()), NULL, 1, DATEADD(day, -2, GETUTCDATE())),
(5, 5, 5, DATEADD(day, -1, GETUTCDATE()), DATEADD(day, 13, GETUTCDATE()), NULL, 1, DATEADD(day, -1, GETUTCDATE())),
(6, 6, 6, DATEADD(day, -4, GETUTCDATE()), DATEADD(day, 10, GETUTCDATE()), NULL, 1, DATEADD(day, -4, GETUTCDATE())),

-- Sách quá hạn (Status = 3) - 3 bản ghi
(7, 7, 7, DATEADD(day, -20, GETUTCDATE()), DATEADD(day, -6, GETUTCDATE()), NULL, 3, DATEADD(day, -20, GETUTCDATE())),
(8, 8, 8, DATEADD(day, -18, GETUTCDATE()), DATEADD(day, -4, GETUTCDATE()), NULL, 3, DATEADD(day, -18, GETUTCDATE())),
(9, 9, 9, DATEADD(day, -25, GETUTCDATE()), DATEADD(day, -11, GETUTCDATE()), NULL, 3, DATEADD(day, -25, GETUTCDATE())),

-- Sách đã trả (Status = 2) - 3 bản ghi
(10, 10, 10, DATEADD(day, -25, GETUTCDATE()), DATEADD(day, -11, GETUTCDATE()), DATEADD(day, -12, GETUTCDATE()), 2, DATEADD(day, -25, GETUTCDATE())),
(11, 11, 1, DATEADD(day, -30, GETUTCDATE()), DATEADD(day, -16, GETUTCDATE()), DATEADD(day, -17, GETUTCDATE()), 2, DATEADD(day, -30, GETUTCDATE())),
(12, 12, 2, DATEADD(day, -35, GETUTCDATE()), DATEADD(day, -21, GETUTCDATE()), DATEADD(day, -22, GETUTCDATE()), 2, DATEADD(day, -35, GETUTCDATE()));
SET IDENTITY_INSERT BorrowRecords OFF;

PRINT 'Sample data inserted successfully!';
PRINT 'Login accounts:';
PRINT '- Admin: <EMAIL> / Admin123!';
PRINT '- Librarian 1: <EMAIL> / Librarian123!';
PRINT '- Librarian 2: <EMAIL> / Librarian123!';
PRINT '- Assistant 1: <EMAIL> / Assistant123!';
PRINT '- Assistant 2: <EMAIL> / Assistant123!';
PRINT '';
PRINT 'Data summary:';
PRINT '- Categories: 6';
PRINT '- Users: 5';
PRINT '- Members: 10';
PRINT '- Books: 22 (with real cover images)';
PRINT '- Borrow Records: 12 (6 active, 3 overdue, 3 returned)'; 