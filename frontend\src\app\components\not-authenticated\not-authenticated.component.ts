import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-not-authenticated',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    MatCardModule,
    MatButtonModule,
    MatIconModule
  ],
  template: `
    <div class="not-auth-container">
      <mat-card class="not-auth-card">
        <mat-card-header>
          <div class="icon-container">
            <mat-icon *ngIf="!authService.isAuthenticated">lock</mat-icon>
            <mat-icon *ngIf="authService.isAuthenticated">block</mat-icon>
          </div>
        </mat-card-header>
        
        <mat-card-content>
          <h2 *ngIf="!authService.isAuthenticated">Bạn cần đăng nhập để sử dụng</h2>
          <h2 *ngIf="authService.isAuthenticated">Không có quyền truy cập</h2>
          
          <p *ngIf="!authService.isAuthenticated">
            Để truy cập vào hệ thống quản lý thư viện, bạn cần có tài khoản và đăng nhập.
          </p>
          
          <p *ngIf="authService.isAuthenticated">
            Tài khoản của bạn (<strong>{{authService.currentUserValue?.role}}</strong>) không có đủ quyền hạn để truy cập trang này.
          </p>
          
          <div class="features" *ngIf="!authService.isAuthenticated">
            <h3>Với tài khoản, bạn có thể:</h3>
            <ul>
              <li>
                <mat-icon>book</mat-icon>
                <span>Quản lý thông tin sách</span>
              </li>
              <li>
                <mat-icon>people</mat-icon>
                <span>Quản lý thành viên thư viện</span>
              </li>
              <li>
                <mat-icon>assignment</mat-icon>
                <span>Theo dõi việc mượn/trả sách</span>
              </li>
              <li>
                <mat-icon>analytics</mat-icon>
                <span>Xem báo cáo thống kê</span>
              </li>
            </ul>
          </div>

          <div class="features" *ngIf="authService.isAuthenticated">
            <h3>Phân quyền hệ thống:</h3>
            <ul>
              <li>
                <mat-icon>admin_panel_settings</mat-icon>
                <span><strong>Admin:</strong> Toàn quyền quản lý hệ thống</span>
              </li>
              <li>
                <mat-icon>manage_accounts</mat-icon>
                <span><strong>Librarian:</strong> Quản lý sách, thành viên, mượn/trả</span>
              </li>
              <li>
                <mat-icon>support_agent</mat-icon>
                <span><strong>Assistant:</strong> Chỉ mượn/trả sách</span>
              </li>
            </ul>
          </div>
          
          <div class="actions">
            <button mat-button routerLink="/books" color="primary">
              <mat-icon>home</mat-icon>
              Về trang chủ
            </button>
            <button *ngIf="!authService.isAuthenticated" mat-raised-button color="primary" routerLink="/login">
              <mat-icon>login</mat-icon>
              Đăng nhập
            </button>
            <button *ngIf="!authService.isAuthenticated" mat-button color="accent" routerLink="/register">
              <mat-icon>person_add</mat-icon>
              Đăng ký tài khoản
            </button>
            <button *ngIf="authService.isAuthenticated" mat-button color="warn" (click)="logout()">
              <mat-icon>logout</mat-icon>
              Đăng xuất
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .not-auth-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 20px;
    }

    .not-auth-card {
      width: 100%;
      max-width: 600px;
      padding: 40px;
      text-align: center;
      border-radius: 16px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    mat-card-header {
      display: flex;
      justify-content: center;
      margin-bottom: 30px;
    }

    .icon-container {
      background: #f44336;
      border-radius: 50%;
      padding: 20px;
      margin-bottom: 20px;
    }

    .icon-container mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: white;
    }

    h2 {
      color: #333;
      margin-bottom: 16px;
      font-size: 28px;
      font-weight: 500;
    }

    p {
      color: #666;
      margin-bottom: 30px;
      font-size: 16px;
      line-height: 1.6;
    }

    .features {
      text-align: left;
      margin: 30px 0;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .features h3 {
      color: #333;
      margin-bottom: 16px;
      font-size: 18px;
      text-align: center;
    }

    .features ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .features li {
      display: flex;
      align-items: center;
      padding: 8px 0;
      color: #555;
    }

    .features li mat-icon {
      color: #3f51b5;
      margin-right: 12px;
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    .actions {
      display: flex;
      flex-direction: column;
      gap: 16px;
      align-items: center;
      margin-top: 30px;
    }

    .actions button {
      min-width: 200px;
      padding: 12px 24px;
      font-size: 16px;
    }

    @media (min-width: 768px) {
      .actions {
        flex-direction: row;
        justify-content: center;
      }
    }
  `]
})
export class NotAuthenticatedComponent {
  constructor(public authService: AuthService) {}

  logout(): void {
    this.authService.logout();
    window.location.reload();
  }
} 