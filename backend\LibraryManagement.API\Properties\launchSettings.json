{"$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"LibraryManagement.API": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "https://localhost:7001", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "https://localhost:7001", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}, "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:65351/", "sslPort": 44367}}}