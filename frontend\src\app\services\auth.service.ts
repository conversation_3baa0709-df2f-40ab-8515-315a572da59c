import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import { LoginRequest, RegisterRequest, AuthResponse, User, VerifyEmailRequest, RequestPasswordResetRequest, ResetPasswordRequest, ResendVerificationRequest, ApiResponse } from '../models/auth.model';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private apiUrl = `${environment.apiUrl}/auth`;
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(private http: HttpClient) {
    // Load user from localStorage if exists
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    if (token && user) {
      const parsedUser = JSON.parse(user);
      // Sync với JWT token claims nếu có
      const tokenInfo = this.getTokenInfo();
      if (tokenInfo && !tokenInfo.isExpired) {
        parsedUser.emailVerified = tokenInfo.emailVerified;
      }
      this.currentUserSubject.next(parsedUser);
    }
  }

  get currentUserValue(): User | null {
    return this.currentUserSubject.value;
  }

  get isAuthenticated(): boolean {
    return !!this.currentUserSubject.value;
  }

  // Role checking methods
  get currentUserRole(): string | null {
    return this.currentUserValue?.role || null;
  }

  hasRole(role: string): boolean {
    return this.currentUserRole === role;
  }

  hasAnyRole(roles: string[]): boolean {
    return roles.includes(this.currentUserRole || '');
  }

  isAdmin(): boolean {
    return this.hasRole('Admin');
  }

  isLibrarian(): boolean {
    return this.hasRole('Librarian');
  }

  isAssistant(): boolean {
    return this.hasRole('Assistant');
  }

  // Permission checking methods based on business rules
  canManageBooks(): boolean {
    return this.hasAnyRole(['Admin', 'Librarian']);
  }

  canManageMembers(): boolean {
    return this.hasAnyRole(['Admin', 'Librarian']);
  }

  canHandleBorrowOperations(): boolean {
    return this.hasAnyRole(['Admin', 'Librarian', 'Assistant']);
  }

  canAccessAdminFeatures(): boolean {
    return this.hasRole('Admin');
  }

  login(credentials: LoginRequest): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${this.apiUrl}/login`, credentials)
      .pipe(
        tap(response => {
          this.storeUserData(response);
        })
      );
  }

  register(userData: RegisterRequest): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${this.apiUrl}/register`, userData);
  }

  verifyEmail(request: VerifyEmailRequest): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${this.apiUrl}/verify-email`, request);
  }

  requestPasswordReset(request: RequestPasswordResetRequest): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${this.apiUrl}/request-password-reset`, request);
  }

  resetPassword(request: ResetPasswordRequest): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${this.apiUrl}/reset-password`, request);
  }

  resendVerification(request: ResendVerificationRequest): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${this.apiUrl}/resend-verification`, request);
  }

  // Debug method để decode JWT token
  getTokenInfo(tokenParam?: string): any {
    const token = tokenParam || this.getToken();
    if (!token) return null;
    
    try {
      const payload = token.split('.')[1];
      const decoded = JSON.parse(atob(payload));
      return {
        claims: decoded,
        isExpired: decoded.exp < Date.now() / 1000,
        emailVerified: decoded.EmailVerified === 'True'
      };
    } catch (error) {
      return null;
    }
  }

  // Method để refresh user info mà không cần login lại
  refreshUserInfo(): Observable<User> {
    return this.http.get<User>(`${this.apiUrl.replace('/auth', '')}/user/profile`)
      .pipe(
        tap(user => {
          // Cập nhật localStorage và subject
          localStorage.setItem('user', JSON.stringify(user));
          this.currentUserSubject.next(user);
        })
      );
  }

  // Method để sync user với JWT token claims
  syncUserWithToken(): void {
    const user = this.currentUserValue;
    const tokenInfo = this.getTokenInfo();
    
    if (user && tokenInfo && !tokenInfo.isExpired) {
      const updatedUser = { ...user };
      updatedUser.emailVerified = tokenInfo.emailVerified;
      
      localStorage.setItem('user', JSON.stringify(updatedUser));
      this.currentUserSubject.next(updatedUser);
    }
  }

  logout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    this.currentUserSubject.next(null);
  }

  getToken(): string | null {
    return localStorage.getItem('token');
  }

  private storeUserData(response: AuthResponse): void {
    localStorage.setItem('token', response.token);
    
    // Sync user object với JWT claims
    const user = { ...response.user };
    const tokenInfo = this.getTokenInfo(response.token);
    if (tokenInfo && !tokenInfo.isExpired) {
      user.emailVerified = tokenInfo.emailVerified;
    }
    
    localStorage.setItem('user', JSON.stringify(user));
    this.currentUserSubject.next(user);
  }
} 