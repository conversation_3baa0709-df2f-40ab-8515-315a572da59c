import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatCardModule } from '@angular/material/card';
import { BorrowService, BorrowRecord, ReturnBookRequest } from '../../services/borrow.service';

@Component({
  selector: 'app-return-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatCardModule
  ],
  template: `
    <div class="return-modal">
      <h2 mat-dialog-title>
        <mat-icon>assignment_return</mat-icon>
        Trả sách
      </h2>

      <form [formGroup]="returnForm" (ngSubmit)="onSubmit()">
        <mat-dialog-content>
          <div class="form-content">
            <!-- Borrow Record Info -->
            <mat-card class="borrow-info">
              <mat-card-header>
                <mat-card-title>Thông tin mượn sách</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="info-grid">
                  <div class="info-item">
                    <span class="label">Sách:</span>
                    <span class="value">{{ borrowRecord.bookTitle }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Tác giả:</span>
                    <span class="value">{{ borrowRecord.bookAuthor }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Thành viên:</span>
                    <span class="value">{{ borrowRecord.memberName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Ngày mượn:</span>
                    <span class="value">{{ formatDate(borrowRecord.borrowDate) }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Hạn trả:</span>
                    <span class="value" [class.overdue]="borrowRecord.isOverdue">
                      {{ formatDate(borrowRecord.dueDate) }}
                    </span>
                  </div>
                  <div class="info-item" *ngIf="borrowRecord.isOverdue">
                    <span class="label">Số ngày quá hạn:</span>
                    <span class="value overdue">{{ borrowRecord.daysOverdue }} ngày</span>
                  </div>
                </div>
                
                <div class="overdue-warning" *ngIf="borrowRecord.isOverdue">
                  <mat-icon>warning</mat-icon>
                  <span>Sách này đã quá hạn {{ borrowRecord.daysOverdue }} ngày</span>
                </div>
              </mat-card-content>
            </mat-card>

            <!-- Return Date -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Ngày trả</mat-label>
              <input matInput 
                     [matDatepicker]="picker" 
                     formControlName="returnDate"
                     [max]="maxDate">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
              <mat-error *ngIf="returnForm.get('returnDate')?.hasError('required')">
                Vui lòng chọn ngày trả
              </mat-error>
            </mat-form-field>

            <!-- Fine Calculation -->
            <div class="fine-section" *ngIf="showFineCalculation">
              <mat-card class="fine-card">
                <mat-card-header>
                  <mat-card-title>
                    <mat-icon>monetization_on</mat-icon>
                    Tính phạt
                  </mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <div class="fine-calculation">
                    <div class="calculation-row">
                      <span>Số ngày quá hạn:</span>
                      <span class="highlight">{{ calculateOverdueDays() }} ngày</span>
                    </div>
                    <div class="calculation-row">
                      <span>Phạt mỗi ngày:</span>
                      <span class="highlight">{{ formatCurrency(finePerDay) }}</span>
                    </div>
                    <div class="calculation-row total">
                      <span>Tổng phạt:</span>
                      <span class="highlight">{{ formatCurrency(calculateAutoFine()) }}</span>
                    </div>
                  </div>

                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Số tiền phạt</mat-label>
                    <input matInput 
                           type="number" 
                           formControlName="fine"
                           [placeholder]="calculateAutoFine().toString()">
                    <span matSuffix>VND</span>
                    <mat-hint>Để trống để áp dụng phạt tự động</mat-hint>
                  </mat-form-field>
                </mat-card-content>
              </mat-card>
            </div>

            <!-- Notes -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Ghi chú khi trả (tùy chọn)</mat-label>
              <textarea matInput 
                        formControlName="notes"
                        rows="3"
                        maxlength="500"
                        placeholder="Nhập ghi chú về tình trạng sách, lý do phạt..."></textarea>
              <mat-hint>{{ returnForm.get('notes')?.value?.length || 0 }}/500</mat-hint>
            </mat-form-field>

            <!-- Existing Notes -->
            <div class="existing-notes" *ngIf="borrowRecord.notes">
              <mat-card>
                <mat-card-header>
                  <mat-card-title>Ghi chú khi mượn</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  {{ borrowRecord.notes }}
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-dialog-content>

        <mat-dialog-actions align="end">
          <button mat-button 
                  type="button" 
                  (click)="onCancel()">
            Hủy
          </button>
          <button mat-raised-button 
                  color="primary" 
                  type="submit"
                  [disabled]="returnForm.invalid || loading">
            <mat-icon *ngIf="loading">hourglass_empty</mat-icon>
            <mat-icon *ngIf="!loading">assignment_return</mat-icon>
            {{ loading ? 'Đang xử lý...' : 'Trả sách' }}
          </button>
        </mat-dialog-actions>
      </form>
    </div>
  `,
  styles: [`
    .return-modal {
      min-width: 600px;
      max-width: 700px;
    }

    h2[mat-dialog-title] {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 20px;
    }

    .form-content {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .full-width {
      width: 100%;
    }

    .borrow-info {
      margin-bottom: 16px;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      margin-bottom: 16px;
    }

    .info-item {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .label {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }

    .value {
      font-size: 14px;
      font-weight: 400;
    }

    .value.overdue {
      color: #f44336;
      font-weight: 500;
    }

    .overdue-warning {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px;
      background-color: #ffebee;
      border-radius: 8px;
      color: #c62828;
      border-left: 4px solid #f44336;
    }

    .fine-section {
      margin: 16px 0;
    }

    .fine-card {
      background-color: #fff3e0;
      border-left: 4px solid #ff9800;
    }

    .fine-calculation {
      margin-bottom: 16px;
    }

    .calculation-row {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
    }

    .calculation-row.total {
      border-top: 2px solid #ff9800;
      font-weight: 500;
      font-size: 16px;
    }

    .highlight {
      font-weight: 500;
      color: #ff9800;
    }

    .existing-notes {
      margin-top: 16px;
    }

    .existing-notes mat-card {
      background-color: #f5f5f5;
    }

    mat-dialog-actions {
      padding: 20px 0 0 0;
      margin: 0;
    }

    @media (max-width: 768px) {
      .return-modal {
        min-width: 90vw;
      }

      .info-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class ReturnModalComponent implements OnInit {
  returnForm: FormGroup;
  loading = false;
  borrowRecord: BorrowRecord;
  maxDate = new Date();
  finePerDay = 5000; // 5000 VND per day
  showFineCalculation = false;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<ReturnModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { borrowRecord: BorrowRecord },
    private borrowService: BorrowService,
    private snackBar: MatSnackBar
  ) {
    this.borrowRecord = data.borrowRecord;
    
    this.returnForm = this.fb.group({
      returnDate: [new Date(), Validators.required],
      fine: [''],
      notes: ['']
    });

    // Watch for return date changes to calculate fine
    this.returnForm.get('returnDate')?.valueChanges.subscribe(date => {
      this.updateFineCalculation(date);
    });

    // Initial fine calculation
    this.updateFineCalculation(new Date());
  }

  ngOnInit(): void {
    // Set initial state
    this.updateFineCalculation(new Date());
  }

  updateFineCalculation(returnDate: Date): void {
    if (returnDate) {
      const dueDate = new Date(this.borrowRecord.dueDate);
      this.showFineCalculation = returnDate > dueDate;
      
      if (this.showFineCalculation) {
        const autoFine = this.calculateAutoFine();
        // Only set fine if it's not manually entered
        if (!this.returnForm.get('fine')?.value) {
          this.returnForm.patchValue({ fine: autoFine }, { emitEvent: false });
        }
      } else {
        this.returnForm.patchValue({ fine: '' }, { emitEvent: false });
      }
    }
  }

  calculateOverdueDays(): number {
    const returnDate = this.returnForm.get('returnDate')?.value;
    if (!returnDate) return 0;

    const dueDate = new Date(this.borrowRecord.dueDate);
    const returnDateTime = new Date(returnDate);

    if (returnDateTime <= dueDate) return 0;

    const diffTime = returnDateTime.getTime() - dueDate.getTime();
    return Math.floor(diffTime / (1000 * 60 * 60 * 24));
  }

  calculateAutoFine(): number {
    const overdueDays = this.calculateOverdueDays();
    return overdueDays * this.finePerDay;
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  }

  onSubmit(): void {
    if (this.returnForm.valid && !this.loading) {
      this.loading = true;

      const formValue = this.returnForm.value;
      const request: ReturnBookRequest = {
        borrowRecordId: this.borrowRecord.id,
        returnDate: formValue.returnDate.toISOString(),
        fine: formValue.fine ? parseFloat(formValue.fine) : (this.showFineCalculation ? this.calculateAutoFine() : undefined),
        notes: formValue.notes || undefined
      };

      this.borrowService.returnBook(this.borrowRecord.id, request).subscribe({
        next: (result) => {
          let message = 'Trả sách thành công';
          if (request.fine && request.fine > 0) {
            message += ` với phạt ${this.formatCurrency(request.fine)}`;
          }
          this.snackBar.open(message, 'Đóng', { duration: 4000 });
          this.dialogRef.close(result);
        },
        error: (error) => {
          console.error('Error returning book:', error);
          let errorMessage = 'Lỗi khi trả sách';
          if (error.error && error.error.message) {
            errorMessage = error.error.message;
          } else if (error.error && typeof error.error === 'string') {
            errorMessage = error.error;
          }
          this.snackBar.open(errorMessage, 'Đóng', { duration: 5000 });
          this.loading = false;
        }
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }
} 