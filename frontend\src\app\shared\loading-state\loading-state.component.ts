import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-loading-state',
  standalone: true,
  imports: [CommonModule, MatProgressSpinnerModule, MatIconModule],
  template: `
    <div class="loading-container" [class.fullscreen]="fullscreen">
      <div class="loading-content">
        <div class="spinner-container">
          <mat-spinner [diameter]="size" [color]="color"></mat-spinner>
        </div>
        <p class="loading-text" *ngIf="message">{{ message }}</p>
        <p class="loading-subtext" *ngIf="subMessage">{{ subMessage }}</p>
      </div>
    </div>
  `,
  styles: [`
    .loading-container {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-xl);
      min-height: 200px;

      &.fullscreen {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(var(--color-background), 0.8);
        backdrop-filter: blur(4px);
        -webkit-backdrop-filter: blur(4px);
        z-index: 9999;
        min-height: 100vh;
      }
    }

    .loading-content {
      text-align: center;
      max-width: 300px;
    }

    .spinner-container {
      margin-bottom: var(--spacing-lg);
    }

    .loading-text {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-medium);
      color: var(--color-on-surface);
      margin: 0 0 var(--spacing-sm) 0;
    }

    .loading-subtext {
      font-size: var(--font-size-sm);
      color: var(--color-on-surface);
      opacity: 0.7;
      margin: 0;
    }
  `]
})
export class LoadingStateComponent {
  @Input() message: string = 'Đang tải...';
  @Input() subMessage?: string;
  @Input() size: number = 50;
  @Input() color: 'primary' | 'accent' | 'warn' = 'primary';
  @Input() fullscreen: boolean = false;
}
