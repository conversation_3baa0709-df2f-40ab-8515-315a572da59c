.overdue-books-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  color: #3f51b5;
  font-weight: 500;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.actions {
  display: flex;
  gap: 10px;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.error-container {
  color: #f44336;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-container mat-form-field {
  width: 100%;
}

.summary-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  background-color: #f5f5f5;
  padding: 12px 16px;
  border-radius: 4px;
}

.summary-info p {
  margin: 0;
}

.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
}

.mat-column-id {
  width: 80px;
}

.mat-column-actions {
  width: 80px;
  text-align: center;
}

.mat-column-daysOverdue, .mat-column-fine {
  width: 120px;
}

.book-info {
  display: flex;
  flex-direction: column;
}

.book-title {
  font-weight: 500;
}

.book-author {
  font-size: 12px;
  color: #666;
}

.overdue-days {
  font-weight: 500;
  color: #f44336;
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
}

.no-data-message mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  color: #ccc;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .actions {
    margin-top: 16px;
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  
  .summary-info {
    flex-direction: column;
    gap: 8px;
  }
}