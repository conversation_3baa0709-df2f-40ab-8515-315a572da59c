import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatDividerModule } from '@angular/material/divider';
import { MatTableModule } from '@angular/material/table';

import { CategoryService } from '../../services/category.service';
import { Category, CategoryStatistics } from '../../models/category.model';

@Component({
  selector: 'app-category-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTabsModule,
    MatProgressBarModule,
    MatDividerModule,
    MatTableModule
  ],
  template: `
    <div class="category-detail-container" *ngIf="!isLoading">
      <!-- Header Card -->
      <mat-card class="category-header-card">
        <mat-card-header>
          <div mat-card-avatar class="category-avatar">
            <mat-icon>category</mat-icon>
          </div>
          <mat-card-title>{{ category?.name }}</mat-card-title>
          <mat-card-subtitle>
            <mat-icon>info</mat-icon>
            Thông tin chi tiết thể loại
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <!-- Basic Info -->
          <div class="category-info-grid">
            <div class="info-item">
              <mat-icon>book</mat-icon>
              <span class="label">Tổng số sách:</span>
              <span class="value">{{ category?.bookCount || 0 }} cuốn</span>
            </div>

            <div class="info-item">
              <mat-icon>schedule</mat-icon>
              <span class="label">Ngày tạo:</span>
              <span class="value">{{ getFormattedDate(category?.createdAt) }}</span>
            </div>

            <div class="info-item" *ngIf="category?.updatedAt">
              <mat-icon>update</mat-icon>
              <span class="label">Cập nhật:</span>
              <span class="value">{{ getFormattedDate(category?.updatedAt) }}</span>
            </div>
          </div>

          <!-- Description -->
          <div class="description-section" *ngIf="category?.description">
            <mat-divider></mat-divider>
            <h4>
              <mat-icon>description</mat-icon>
              Mô tả
            </h4>
            <p>{{ category?.description }}</p>
          </div>
        </mat-card-content>

        <mat-card-actions align="end">
          <button mat-button (click)="goBack()">
            <mat-icon>arrow_back</mat-icon>
            Quay lại
          </button>
          
          <button mat-raised-button color="primary" [routerLink]="['/categories/edit', category?.id]">
            <mat-icon>edit</mat-icon>
            Chỉnh sửa
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Statistics Card -->
      <mat-card class="statistics-card" *ngIf="statistics">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>analytics</mat-icon>
            Thống kê chi tiết
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <div class="stats-grid">
            <div class="stat-item">
              <mat-icon class="stat-icon total">auto_stories</mat-icon>
              <div class="stat-content">
                <span class="stat-value">{{ statistics.totalBooks }}</span>
                <span class="stat-label">Tổng sách</span>
              </div>
            </div>

            <div class="stat-item">
              <mat-icon class="stat-icon available">check_circle</mat-icon>
              <div class="stat-content">
                <span class="stat-value">{{ statistics.availableBooks }}</span>
                <span class="stat-label">Có sẵn</span>
              </div>
            </div>

            <div class="stat-item">
              <mat-icon class="stat-icon borrowed">schedule</mat-icon>
              <div class="stat-content">
                <span class="stat-value">{{ statistics.borrowedBooks }}</span>
                <span class="stat-label">Đang mượn</span>
              </div>
            </div>

            <div class="stat-item">
              <mat-icon class="stat-icon shelved">inventory</mat-icon>
              <div class="stat-content">
                <span class="stat-value">{{ statistics.booksOnShelves }}</span>
                <span class="stat-label">Trên kệ</span>
              </div>
            </div>

            <div class="stat-item">
              <mat-icon class="stat-icon storage">inventory_2</mat-icon>
              <div class="stat-content">
                <span class="stat-value">{{ statistics.booksInStorage }}</span>
                <span class="stat-label">Trong kho</span>
              </div>
            </div>
          </div>

          <!-- Availability Progress -->
          <div class="availability-section">
            <div class="availability-header">
              <span>Tỷ lệ sách có sẵn</span>
              <span>{{ getAvailabilityPercentage() }}%</span>
            </div>
            <mat-progress-bar 
              mode="determinate" 
              [value]="getAvailabilityPercentage()"
              [color]="getProgressBarColor()">
            </mat-progress-bar>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Popular Books Card -->
      <mat-card class="popular-books-card" *ngIf="hasPopularBooks()">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>trending_up</mat-icon>
            Sách phổ biến nhất ({{ statistics?.popularBooks?.length }})
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <table mat-table [dataSource]="statistics?.popularBooks || []">
            <ng-container matColumnDef="title">
              <th mat-header-cell *matHeaderCellDef>Tên sách</th>
              <td mat-cell *matCellDef="let book">
                <div class="book-info">
                  <strong>{{ book.title }}</strong>
                  <small>{{ book.author }}</small>
                </div>
              </td>
            </ng-container>

            <ng-container matColumnDef="borrowCount">
              <th mat-header-cell *matHeaderCellDef>Lượt mượn</th>
              <td mat-cell *matCellDef="let book">
                <span class="borrow-count-chip">{{ book.borrowCount }} lượt</span>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="popularBooksColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: popularBooksColumns;"></tr>
          </table>
        </mat-card-content>
      </mat-card>

      <!-- Empty Popular Books State -->
      <mat-card class="popular-books-card" *ngIf="statistics && statistics.popularBooks.length === 0">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>trending_up</mat-icon>
            Sách phổ biến
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <div class="empty-state">
            <mat-icon class="empty-icon">book_online</mat-icon>
            <h3>Chưa có dữ liệu mượn sách</h3>
            <p>Thể loại này chưa có sách nào được mượn</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="loading-container">
      <mat-spinner diameter="50"></mat-spinner>
      <p>Đang tải thông tin thể loại...</p>
    </div>
  `,
  styles: [`
    .category-detail-container {
      max-width: 1200px;
      margin: 20px auto;
      padding: 0 16px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .category-header-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .category-header-card mat-card-title,
    .category-header-card mat-card-subtitle {
      color: white;
    }

    .category-avatar {
      background-color: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .category-info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 20px;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .info-item .label {
      font-weight: 500;
    }

    .info-item .value {
      font-weight: 600;
    }

    .description-section {
      margin-top: 20px;
    }

    .description-section h4 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 16px 0 8px 0;
    }

    .statistics-card,
    .popular-books-card {
      background: white;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 20px;
      margin-bottom: 24px;
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    .stat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .stat-icon.total { color: #666eea; }
    .stat-icon.available { color: #4caf50; }
    .stat-icon.borrowed { color: #ff9800; }
    .stat-icon.shelved { color: #2196f3; }
    .stat-icon.storage { color: #9c27b0; }

    .stat-content {
      display: flex;
      flex-direction: column;
    }

    .stat-value {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .stat-label {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.6);
    }

    .availability-section {
      border-top: 1px solid #e0e0e0;
      padding-top: 20px;
    }

    .availability-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .book-info {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .book-info small {
      color: rgba(0, 0, 0, 0.6);
      font-size: 12px;
    }

    .borrow-count-chip {
      display: inline-block;
      padding: 4px 8px;
      background-color: #e8f5e8;
      color: #2e7d32;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: rgba(0, 0, 0, 0.6);
    }

    .empty-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 400px;
      gap: 20px;
    }

    @media (max-width: 768px) {
      .category-info-grid {
        grid-template-columns: 1fr;
      }
      
      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  `]
})
export class CategoryDetailComponent implements OnInit {
  category?: Category;
  statistics?: CategoryStatistics;
  isLoading = true;
  categoryId!: number;

  popularBooksColumns = ['title', 'borrowCount'];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private categoryService: CategoryService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.categoryId = parseInt(this.route.snapshot.paramMap.get('id') || '0');
    if (this.categoryId) {
      this.loadCategoryData();
      this.loadStatistics();
    } else {
      this.router.navigate(['/categories']);
    }
  }

  private loadCategoryData(): void {
    this.categoryService.getCategoryById(this.categoryId).subscribe({
      next: (category) => {
        this.category = category;
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi tải thông tin thể loại: ' + error.message, 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
        this.router.navigate(['/categories']);
      }
    });
  }

  private loadStatistics(): void {
    this.categoryService.getCategoryStatistics(this.categoryId).subscribe({
      next: (statistics) => {
        this.statistics = statistics;
      },
      error: (error) => {
        console.error('Error loading category statistics:', error);
      }
    });
  }

  getAvailabilityPercentage(): number {
    if (!this.statistics || this.statistics.totalBooks === 0) return 0;
    return Math.round((this.statistics.availableBooks / this.statistics.totalBooks) * 100);
  }

  getProgressBarColor(): string {
    const percentage = this.getAvailabilityPercentage();
    if (percentage >= 70) return 'primary';
    if (percentage >= 30) return 'accent';
    return 'warn';
  }

  getFormattedDate(date?: Date): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('vi-VN');
  }

  hasPopularBooks(): boolean {
    return !!(this.statistics?.popularBooks && this.statistics.popularBooks.length > 0);
  }

  goBack(): void {
    this.router.navigate(['/categories']);
  }
}
