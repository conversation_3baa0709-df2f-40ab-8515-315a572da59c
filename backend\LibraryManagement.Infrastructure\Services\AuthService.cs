using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using LibraryManagement.Core.Entities;
using LibraryManagement.Core.Enums;
using LibraryManagement.Core.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;

namespace LibraryManagement.Infrastructure.Services;

public class AuthService : IAuthService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IConfiguration _configuration;
    private readonly IEmailService _emailService;

    public AuthService(IUnitOfWork unitOfWork, IConfiguration configuration, IEmailService emailService)
    {
        _unitOfWork = unitOfWork;
        _configuration = configuration;
        _emailService = emailService;
    }

    public async Task<(bool Success, string Token, User? User, string Message)> LoginAsync(string email, string password)
    {
        try
        {
            var users = await _unitOfWork.Users.FindAsync(u => u.Email == email);
            var user = users.FirstOrDefault();

            if (user == null)
            {
                return (false, string.Empty, null, "Email hoặc mật khẩu không đúng");
            }

            if (!user.EmailVerified)
            {
                return (false, string.Empty, null, "Vui lòng xác nhận email trước khi đăng nhập");
            }

            if (!user.IsActive)
            {
                return (false, string.Empty, null, "Tài khoản đã bị khóa");
            }

            if (!VerifyPassword(password, user.PasswordHash))
            {
                return (false, string.Empty, null, "Email hoặc mật khẩu không đúng");
            }

            // Update last login date
            user.LastLoginDate = DateTime.UtcNow;
            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            var token = GenerateJwtToken(user);
            return (true, token, user, "Đăng nhập thành công");
        }
        catch (Exception ex)
        {
            return (false, string.Empty, null, $"Lỗi hệ thống: {ex.Message}");
        }
    }

    public async Task<(bool Success, string Token, User? User, string Message)> RegisterAsync(
        string email, string password, string username, string firstName, string lastName)
    {
        try
        {
            // Check if email already exists
            var existingEmailUsers = await _unitOfWork.Users.FindAsync(u => u.Email == email);
            if (existingEmailUsers.Any())
            {
                return (false, string.Empty, null, "Email đã được sử dụng");
            }

            // Check if username already exists
            var existingUsernameUsers = await _unitOfWork.Users.FindAsync(u => u.Username == username);
            if (existingUsernameUsers.Any())
            {
                return (false, string.Empty, null, "Username đã được sử dụng");
            }

            var verificationToken = Guid.NewGuid().ToString();

            var user = new User
            {
                Email = email,
                Username = username,
                FirstName = firstName,
                LastName = lastName,
                PasswordHash = HashPassword(password),
                Role = UserRole.Assistant,
                IsActive = false, // Will be activated after email verification
                EmailVerified = false,
                EmailVerificationToken = verificationToken,
                EmailVerificationTokenExpiry = DateTime.UtcNow.AddDays(1) // 24 hours
            };

            await _unitOfWork.Users.AddAsync(user);
            await _unitOfWork.SaveChangesAsync();

            // Send verification email
            await _emailService.SendEmailVerificationAsync(email, firstName, verificationToken);

            return (true, string.Empty, user, "Đăng ký thành công! Vui lòng kiểm tra email để xác nhận tài khoản.");
        }
        catch (Exception ex)
        {
            return (false, string.Empty, null, $"Lỗi hệ thống: {ex.Message}");
        }
    }

    public async Task<(bool Success, string Message)> VerifyEmailAsync(string token)
    {
        try
        {
            var users = await _unitOfWork.Users.FindAsync(u => 
                u.EmailVerificationToken == token && 
                u.EmailVerificationTokenExpiry > DateTime.UtcNow);
            
            var user = users.FirstOrDefault();
            
            if (user == null)
            {
                return (false, "Token không hợp lệ hoặc đã hết hạn");
            }

            user.EmailVerified = true;
            user.IsActive = true;
            user.EmailVerificationToken = null;
            user.EmailVerificationTokenExpiry = null;

            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            // Send welcome email
            await _emailService.SendWelcomeEmailAsync(user.Email, user.FirstName);

            return (true, "Email đã được xác nhận thành công!");
        }
        catch (Exception ex)
        {
            return (false, $"Lỗi hệ thống: {ex.Message}");
        }
    }

    public async Task<(bool Success, string Message)> RequestPasswordResetAsync(string email)
    {
        try
        {
            var users = await _unitOfWork.Users.FindAsync(u => u.Email == email);
            var user = users.FirstOrDefault();

            if (user == null)
            {
                // Don't reveal if email exists for security
                return (true, "Nếu email tồn tại, bạn sẽ nhận được hướng dẫn đặt lại mật khẩu");
            }

            var resetToken = Guid.NewGuid().ToString();
            user.PasswordResetToken = resetToken;
            user.PasswordResetTokenExpiry = DateTime.UtcNow.AddHours(1); // 1 hour

            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            await _emailService.SendPasswordResetAsync(user.Email, user.FirstName, resetToken);

            return (true, "Nếu email tồn tại, bạn sẽ nhận được hướng dẫn đặt lại mật khẩu");
        }
        catch (Exception ex)
        {
            return (false, $"Lỗi hệ thống: {ex.Message}");
        }
    }

    public async Task<(bool Success, string Message)> ResetPasswordAsync(string token, string newPassword)
    {
        try
        {
            var users = await _unitOfWork.Users.FindAsync(u => 
                u.PasswordResetToken == token && 
                u.PasswordResetTokenExpiry > DateTime.UtcNow);
            
            var user = users.FirstOrDefault();
            
            if (user == null)
            {
                return (false, "Token không hợp lệ hoặc đã hết hạn");
            }

            user.PasswordHash = HashPassword(newPassword);
            user.PasswordResetToken = null;
            user.PasswordResetTokenExpiry = null;

            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            return (true, "Mật khẩu đã được đặt lại thành công!");
        }
        catch (Exception ex)
        {
            return (false, $"Lỗi hệ thống: {ex.Message}");
        }
    }

    public async Task<(bool Success, string Message)> ResendVerificationEmailAsync(string email)
    {
        try
        {
            var users = await _unitOfWork.Users.FindAsync(u => u.Email == email && !u.EmailVerified);
            var user = users.FirstOrDefault();

            if (user == null)
            {
                return (false, "Email không tồn tại hoặc đã được xác nhận");
            }

            var verificationToken = Guid.NewGuid().ToString();
            user.EmailVerificationToken = verificationToken;
            user.EmailVerificationTokenExpiry = DateTime.UtcNow.AddDays(1);

            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            await _emailService.SendEmailVerificationAsync(user.Email, user.FirstName, verificationToken);

            return (true, "Email xác nhận đã được gửi lại");
        }
        catch (Exception ex)
        {
            return (false, $"Lỗi hệ thống: {ex.Message}");
        }
    }

    public async Task<(bool Success, string Message)> ForceVerifyEmailAsync(int userId, int adminId)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                return (false, "Không tìm thấy người dùng");
            }

            var admin = await _unitOfWork.Users.GetByIdAsync(adminId);
            if (admin == null)
            {
                return (false, "Không tìm thấy admin");
            }

            if (user.EmailVerified)
            {
                return (false, "Email đã được xác thực trước đó");
            }

            user.EmailVerified = true;
            user.IsActive = true;
            user.EmailVerificationMethod = EmailVerificationMethod.AdminForce;
            user.EmailVerifiedAt = DateTime.UtcNow;
            user.EmailVerifiedById = adminId;
            user.EmailVerificationToken = null;
            user.EmailVerificationTokenExpiry = null;

            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            // Gửi email thông báo cho user
            await _emailService.SendEmailVerificationConfirmationAsync(
                user.Email,
                user.FirstName,
                admin.FullName,
                "Xác thực bởi Admin"
            );

            return (true, "Email đã được xác thực thành công");
        }
        catch (Exception ex)
        {
            return (false, $"Lỗi hệ thống: {ex.Message}");
        }
    }

    public string GenerateJwtToken(User user)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(_configuration["Jwt:Key"] ?? throw new InvalidOperationException("JWT Key not configured"));
        
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Email, user.Email),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Role, user.Role.ToString()),
                new Claim("FirstName", user.FirstName),
                new Claim("LastName", user.LastName),
                new Claim("EmailVerified", user.EmailVerified.ToString())
            }),
            Expires = DateTime.UtcNow.AddDays(Convert.ToDouble(_configuration["Jwt:ExpiryInDays"] ?? "7")),
            Issuer = _configuration["Jwt:Issuer"],
            Audience = _configuration["Jwt:Audience"],
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    public string HashPassword(string password)
    {
        return BCrypt.Net.BCrypt.HashPassword(password);
    }

    public bool VerifyPassword(string password, string hashedPassword)
    {
        return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
    }
} 