import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule, MatTable } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { ReportService, FineCollectionReport, FineDetail } from '../../../services/report.service';

@Component({
  selector: 'app-fine-collection',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressBarModule,
    MatTooltipModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule
  ],
  templateUrl: './fine-collection.component.html',
  styleUrls: ['./fine-collection.component.scss']
})
export class FineCollectionComponent implements OnInit {
  displayedColumns: string[] = [
    'borrowId',
    'bookInfo',
    'memberInfo',
    'returnDate',
    'daysOverdue',
    'fine',
    'actions'
  ];
  fineReport: FineCollectionReport | null = null;
  filteredData: FineDetail[] = [];
  isLoading = true;
  error = '';
  filterValue = '';
  startDate: Date | null = null;
  endDate: Date | null = null;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild(MatTable) table!: MatTable<FineDetail>;

  constructor(
    private reportService: ReportService,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    // Set default date range to current month
    const today = new Date();
    this.startDate = new Date(today.getFullYear(), today.getMonth(), 1);
    this.endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    this.loadFineCollection();
  }

  loadFineCollection(): void {
    this.isLoading = true;
    this.error = '';

    this.reportService.getFineCollection(this.startDate || undefined, this.endDate || undefined).subscribe({
      next: (data) => {
        this.fineReport = data;
        this.filteredData = data.fineDetails;
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Không thể tải dữ liệu thu phí phạt';
        this.isLoading = false;
        this.snackBar.open(this.error, 'Đóng', { duration: 3000 });
      }
    });
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value.trim().toLowerCase();
    this.filterValue = filterValue;

    if (filterValue && this.fineReport) {
      this.filteredData = this.fineReport.fineDetails.filter(detail =>
        detail.borrowId.toString().includes(filterValue) ||
        detail.bookId.toString().includes(filterValue) ||
        detail.memberId.toString().includes(filterValue)
      );
    } else if (this.fineReport) {
      this.filteredData = this.fineReport.fineDetails;
    }

    if (this.table) {
      this.table.renderRows();
    }
  }

  clearFilter(): void {
    this.filterValue = '';
    if (this.fineReport) {
      this.filteredData = this.fineReport.fineDetails;
    }
    if (this.table) {
      this.table.renderRows();
    }
  }

  applyDateFilter(): void {
    if (this.startDate && this.endDate) {
      this.loadFineCollection();
    } else {
      this.snackBar.open('Vui lòng chọn khoảng thời gian hợp lệ', 'Đóng', { duration: 3000 });
    }
  }

  resetDateFilter(): void {
    const today = new Date();
    this.startDate = new Date(today.getFullYear(), today.getMonth(), 1);
    this.endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    this.loadFineCollection();
  }

  exportToCsv(): void {
    if (!this.fineReport || this.fineReport.fineDetails.length === 0) {
      this.snackBar.open('Không có dữ liệu để xuất', 'Đóng', { duration: 3000 });
      return;
    }

    // Define CSV headers
    const headers = [
      'ID mượn',
      'ID sách',
      'ID thành viên',
      'Ngày trả',
      'Số ngày quá hạn',
      'Phí phạt'
    ];

    // Convert data to CSV format
    const csvData = this.filteredData.map(detail => [
      detail.borrowId,
      detail.bookId,
      detail.memberId,
      new Date(detail.returnDate).toLocaleDateString('vi-VN'),
      detail.daysOverdue,
      detail.fine.toFixed(2)
    ]);

    // Add summary row
    csvData.push([
      '',
      '',
      '',
      '',
      'Tổng phí phạt:',
      this.getTotalFine().toFixed(2)
    ]);

    // Combine headers and data
    const csvContent = [
      headers.join(','),
      ...csvData.map(row => row.join(','))
    ].join('\n');

    // Create a Blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `thu-phi-phat-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  printReport(): void {
    if (!this.fineReport || this.fineReport.fineDetails.length === 0) {
      this.snackBar.open('Không có dữ liệu để in', 'Đóng', { duration: 3000 });
      return;
    }

    const printContent = document.createElement('div');
    printContent.innerHTML = `
      <h1 style="text-align: center;">Báo cáo thu phí phạt</h1>
      <p style="text-align: center;">Từ ngày: ${this.startDate?.toLocaleDateString('vi-VN')} đến ngày: ${this.endDate?.toLocaleDateString('vi-VN')}</p>
      <p style="text-align: center;">Ngày xuất báo cáo: ${new Date().toLocaleDateString('vi-VN')}</p>
      
      <div style="margin: 20px 0; padding: 10px; background-color: #f5f5f5; border-radius: 4px;">
        <p><strong>Tổng số phiếu phạt:</strong> ${this.filteredData.length}</p>
        <p><strong>Tổng phí phạt:</strong> ${this.getTotalFine().toLocaleString('vi-VN')} VND</p>
        <p><strong>Phí phạt trung bình:</strong> ${this.fineReport.averageFine.toLocaleString('vi-VN')} VND</p>
        <p><strong>Phí phạt cao nhất:</strong> ${this.fineReport.maxFine.toLocaleString('vi-VN')} VND</p>
      </div>
      
      <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
        <thead>
          <tr>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">ID mượn</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">ID sách</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">ID thành viên</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Ngày trả</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Số ngày quá hạn</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Phí phạt</th>
          </tr>
        </thead>
        <tbody>
          ${this.filteredData.map(detail => `
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;">${detail.borrowId}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${detail.bookId}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${detail.memberId}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${new Date(detail.returnDate).toLocaleDateString('vi-VN')}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${detail.daysOverdue}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${detail.fine.toLocaleString('vi-VN')} VND</td>
            </tr>
          `).join('')}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="5" style="border: 1px solid #ddd; padding: 8px; text-align: right;"><strong>Tổng phí phạt:</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${this.getTotalFine().toLocaleString('vi-VN')} VND</td>
          </tr>
        </tfoot>
      </table>
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Báo cáo thu phí phạt</title>
            <style>
              body { font-family: Arial, sans-serif; }
              table { width: 100%; border-collapse: collapse; }
              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              th { background-color: #f2f2f2; }
              h1, p { text-align: center; }
              @media print {
                body { margin: 0; padding: 20px; }
              }
            </style>
          </head>
          <body>
            ${printContent.innerHTML}
          </body>
        </html>
      `);
      printWindow.document.close();
      setTimeout(() => {
        printWindow.print();
      }, 500);
    } else {
      this.snackBar.open('Không thể mở cửa sổ in. Vui lòng kiểm tra cài đặt trình duyệt của bạn.', 'Đóng', { duration: 5000 });
    }
  }

  getTotalFine(): number {
    return this.filteredData.reduce((sum, detail) => sum + detail.fine, 0);
  }
}