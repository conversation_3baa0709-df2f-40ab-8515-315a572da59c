.custom-report-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-title {
  margin-bottom: 20px;
  
  h1 {
    font-size: 24px;
    font-weight: 500;
    color: #3f51b5;
    margin: 0;
  }
}

.report-form-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  mat-card-content {
    padding: 16px;
  }
}

.form-row {
  margin-bottom: 16px;
  
  &.date-range {
    display: flex;
    gap: 16px;
    
    .form-field {
      flex: 1;
    }
  }
}

.form-field {
  width: 100%;
}

.form-actions {
  display: flex;
  gap: 16px;
  margin-top: 24px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  
  p {
    margin-top: 16px;
    color: #666;
  }
}

.error-container {
  margin: 20px 0;
  
  .error-message {
    color: #f44336;
    font-weight: 500;
  }
}

.report-results {
  margin-top: 30px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  
  h2 {
    font-size: 20px;
    font-weight: 500;
    margin: 0;
    color: #3f51b5;
  }
  
  .report-actions {
    display: flex;
    gap: 8px;
  }
}

.report-info {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
  
  p {
    margin: 8px 0;
    font-size: 14px;
  }
}

.report-summary {
  margin-bottom: 20px;
  font-style: italic;
  color: #555;
}

.report-table-container {
  overflow-x: auto;
  margin-bottom: 20px;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #eee;
  }
  
  th {
    background-color: #f5f5f5;
    font-weight: 500;
    color: #333;
  }
  
  tr:hover {
    background-color: #f9f9f9;
  }
}

.report-footer {
  margin-top: 20px;
  font-weight: 500;
  color: #555;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-top: 20px;
  
  p {
    color: #666;
    font-style: italic;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-row.date-range {
    flex-direction: column;
    gap: 0;
  }
  
  .report-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .report-table {
    th, td {
      padding: 8px 12px;
      font-size: 14px;
    }
  }
}