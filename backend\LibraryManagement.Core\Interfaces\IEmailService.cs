namespace LibraryManagement.Core.Interfaces;

public interface IEmailService
{
    Task SendEmailVerificationAsync(string email, string firstName, string verificationToken);
    Task SendPasswordResetAsync(string email, string firstName, string resetToken);
    Task SendWelcomeEmailAsync(string email, string firstName);
    Task SendEmailVerificationConfirmationAsync(string email, string firstName, string verifiedByName, string verificationMethod);
} 