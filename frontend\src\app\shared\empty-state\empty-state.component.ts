import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-empty-state',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatIconModule],
  template: `
    <div class="empty-state-container">
      <div class="empty-state-content">
        <div class="empty-icon">
          <mat-icon>{{ icon }}</mat-icon>
        </div>
        <h3 class="empty-title">{{ title }}</h3>
        <p class="empty-message" *ngIf="message">{{ message }}</p>
        <div class="empty-actions" *ngIf="actionText">
          <button 
            type="button"
            mat-raised-button 
            color="primary" 
            (click)="onAction()"
            [disabled]="actionDisabled">
            <mat-icon *ngIf="actionIcon">{{ actionIcon }}</mat-icon>
            {{ actionText }}
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .empty-state-container {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-xxl) var(--spacing-xl);
      min-height: 300px;
      text-align: center;
    }

    .empty-state-content {
      max-width: 400px;
    }

    .empty-icon {
      margin-bottom: var(--spacing-xl);
      
      mat-icon {
        font-size: 80px;
        width: 80px;
        height: 80px;
        color: var(--color-on-surface);
        opacity: 0.3;
      }
    }

    .empty-title {
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-on-surface);
      margin: 0 0 var(--spacing-md) 0;
    }

    .empty-message {
      font-size: var(--font-size-base);
      color: var(--color-on-surface);
      opacity: 0.7;
      line-height: 1.6;
      margin: 0 0 var(--spacing-xl) 0;
    }

    .empty-actions {
      button {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        margin: 0 auto;
      }
    }
  `]
})
export class EmptyStateComponent {
  @Input() icon: string = 'inbox';
  @Input() title: string = 'Không có dữ liệu';
  @Input() message?: string;
  @Input() actionText?: string;
  @Input() actionIcon?: string;
  @Input() actionDisabled: boolean = false;
  
  @Output() action = new EventEmitter<void>();

  onAction(): void {
    this.action.emit();
  }
}
