.dashboard-container {
  padding: 0;
  max-width: none;
  width: 100%;
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, var(--color-background) 0%, var(--color-surface-variant) 100%);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-outline);
  box-shadow: var(--shadow-sm);

  .header-content {
    .dashboard-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin: 0 0 var(--spacing-xs) 0;
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-on-surface);

      .title-icon {
        font-size: 36px;
        width: 36px;
        height: 36px;
        color: var(--color-primary);
      }
    }

    .dashboard-subtitle {
      margin: 0;
      color: var(--color-on-surface);
      opacity: 0.7;
      font-size: var(--font-size-lg);
    }
  }

  .header-actions {
    .refresh-btn {
      background-color: var(--color-primary);
      color: var(--color-on-primary);
      transition: all var(--transition-fast);

      &:hover {
        background-color: var(--color-primary-variant);
        transform: scale(1.1) rotate(180deg);
      }
    }
  }
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--spacing-xl);

  .loading-content {
    text-align: center;

    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid var(--color-outline);
      border-top: 4px solid var(--color-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto var(--spacing-lg);
    }

    .loading-text {
      font-size: var(--font-size-lg);
      color: var(--color-on-surface);
      opacity: 0.7;
      margin: 0;
    }
  }
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--spacing-xl);

  .error-content {
    text-align: center;
    max-width: 400px;

    .error-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: var(--color-error);
      margin-bottom: var(--spacing-lg);
    }

    h3 {
      margin: 0 0 var(--spacing-md) 0;
      font-size: var(--font-size-xl);
      color: var(--color-on-surface);
    }

    .error-message {
      margin: 0 0 var(--spacing-lg) 0;
      color: var(--color-error);
      font-size: var(--font-size-base);
    }

    button {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      margin: 0 auto;
    }
  }
}

.dashboard-content {
  padding: var(--spacing-xl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xxl);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
}

.stat-card {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
  border: none;
  background: var(--color-surface);

  &:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  }

  .mat-mdc-card-content {
    padding: var(--spacing-xl);
  }

  .stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);

    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: var(--radius-large);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      mat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
        color: white;
        z-index: 1;
      }

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        opacity: 0.1;
        background: currentColor;
      }
    }

    .stat-trend {
      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;

        &.trend-up {
          color: var(--color-success);
        }

        &.trend-down {
          color: var(--color-error);
        }

        &.trend-neutral {
          color: var(--color-warning);
        }
      }
    }
  }

  .stat-content {
    .stat-number {
      font-size: 2.5rem;
      font-weight: var(--font-weight-bold);
      color: var(--color-on-surface);
      margin: 0 0 var(--spacing-xs) 0;
      line-height: 1;
    }

    .stat-label {
      font-size: var(--font-size-lg);
      color: var(--color-on-surface);
      opacity: 0.8;
      margin: 0 0 var(--spacing-sm) 0;
      font-weight: var(--font-weight-medium);
    }

    .stat-detail {
      .detail-text {
        font-size: var(--font-size-sm);
        color: var(--color-on-surface);
        opacity: 0.6;
        font-weight: var(--font-weight-normal);
      }
    }
  }

  // Card-specific colors
  &.books-card {
    .stat-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }

  &.members-card {
    .stat-icon {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
  }

  &.borrows-card {
    .stat-icon {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
  }

  &.overdue-card {
    .stat-icon {
      background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.stat-info p {
  margin: 4px 0 0;
  color: #666;
  font-size: 14px;
}

/* Charts Section */
.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 24px;
}

.chart-card {
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.chart-card mat-card-content {
  height: 300px;
  padding: 16px;
}

/* Tables Section */
.tables-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 24px;
}

.table-card {
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.book-info {
  display: flex;
  align-items: center;
}

.book-thumbnail {
  width: 40px;
  height: 60px;
  object-fit: cover;
  margin-right: 12px;
  border-radius: 4px;
}

.book-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.book-author {
  font-size: 12px;
  color: #666;
}

/* Status styles */
.status-active {
  color: #4caf50;
  font-weight: 500;
}

.status-suspended {
  color: #ff9800;
  font-weight: 500;
}

.status-expired {
  color: #9e9e9e;
  font-weight: 500;
}

.status-banned {
  color: #f44336;
  font-weight: 500;
}

/* Reports Section */
.reports-section mat-card {
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 16px;
}

.report-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 16px;
  border-radius: 8px;
  background-color: #f5f5f5;
  text-decoration: none;
  color: #333;
  transition: background-color 0.3s ease, transform 0.3s ease;
  text-align: center;
}

.report-link:hover {
  background-color: #e8eaf6;
  transform: translateY(-5px);
}

.report-link mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  margin-bottom: 12px;
  color: #3f51b5;
}

.report-link span {
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .charts-section, .tables-section {
    grid-template-columns: 1fr;
  }
  
  .chart-card mat-card-content {
    height: 250px;
  }
}