import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { UserManagement, UserRole } from '../../models/user.model';
import { UserService } from '../../services/user.service';
import { AuthService } from '../../services/auth.service';
import { HasPermissionDirective } from '../../directives/has-permission.directive';
import { ConfirmDialogComponent } from '../shared/confirm-dialog.component';

@Component({
  selector: 'app-user-list',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatSnackBarModule,
    MatProgressBarModule,
    MatChipsModule,
    MatDialogModule,
    HasPermissionDirective
  ],
  template: `
    <div class="user-list-container">
      <div class="header">
        <h2>Quản lý người dùng</h2>
        <button mat-raised-button color="primary" (click)="addUser()" *appHasPermission="'admin'">
          <mat-icon>add</mat-icon>
          Thêm người dùng
        </button>
      </div>

      <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>

      <div class="table-container mat-elevation-z8">
        <table mat-table [dataSource]="dataSource" matSort>
          <!-- Username Column -->
          <ng-container matColumnDef="username">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Tên đăng nhập</th>
            <td mat-cell *matCellDef="let user">{{user.username}}</td>
          </ng-container>

          <!-- Email Column -->
          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
            <td mat-cell *matCellDef="let user">{{user.email}}</td>
          </ng-container>

          <!-- Full Name Column -->
          <ng-container matColumnDef="fullName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Họ và tên</th>
            <td mat-cell *matCellDef="let user">{{user.fullName}}</td>
          </ng-container>

          <!-- Role Column -->
          <ng-container matColumnDef="role">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Vai trò</th>
            <td mat-cell *matCellDef="let user">
              <mat-chip [color]="getRoleColor(user.role)" selected>
                {{user.roleName}}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Trạng thái</th>
            <td mat-cell *matCellDef="let user">
              <mat-chip [color]="user.isActive ? 'primary' : 'warn'" selected>
                {{user.isActive ? 'Hoạt động' : 'Đã khóa'}}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Email Verification Column -->
          <ng-container matColumnDef="emailVerified">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
            <td mat-cell *matCellDef="let user">
              <div class="verification-status">
                <mat-icon [class.verified]="user.emailVerified" [class.unverified]="!user.emailVerified">
                  {{user.emailVerified ? 'check_circle' : 'error'}}
                </mat-icon>
                <span>{{user.emailVerified ? 'Đã xác thực' : 'Chưa xác thực'}}</span>
                <mat-icon *ngIf="user.emailVerified" 
                         [matTooltip]="getVerificationTooltip(user)"
                         class="info-icon">info</mat-icon>
              </div>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Thao tác</th>
            <td mat-cell *matCellDef="let user">
              <button mat-icon-button color="primary" 
                      [matTooltip]="'Chỉnh sửa'"
                      (click)="editUser(user.id)"
                      *appHasPermission="'admin'">
                <mat-icon>edit</mat-icon>
              </button>

              <button mat-icon-button [color]="user.isActive ? 'warn' : 'primary'"
                      [matTooltip]="user.isActive ? 'Khóa tài khoản' : 'Mở khóa tài khoản'"
                      (click)="toggleUserStatus(user)"
                      *appHasPermission="'admin'">
                <mat-icon>{{user.isActive ? 'lock' : 'lock_open'}}</mat-icon>
              </button>

              <button mat-icon-button color="warn"
                      [matTooltip]="'Xóa'"
                      (click)="deleteUser(user)"
                      *appHasPermission="'admin'">
                <mat-icon>delete</mat-icon>
              </button>

              <button mat-icon-button color="primary"
                      [matTooltip]="'Force Verify Email'"
                      (click)="forceVerifyEmail(user)"
                      *ngIf="!user.emailVerified && authService.isAdmin()">
                <mat-icon>verified_user</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <mat-paginator [pageSizeOptions]="[10, 25, 50]" showFirstLastButtons></mat-paginator>
      </div>
    </div>
  `,
  styles: [`
    .user-list-container {
      padding: 20px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .table-container {
      position: relative;
      min-height: 200px;
      overflow: auto;
    }

    table {
      width: 100%;
    }

    .mat-column-actions {
      width: 150px;
      text-align: center;
    }

    .verification-status {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .verified {
      color: #4caf50;
    }

    .unverified {
      color: #f44336;
    }

    .info-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
      color: #666;
      cursor: help;
    }
  `]
})
export class UserListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  displayedColumns: string[] = ['username', 'email', 'fullName', 'role', 'status', 'emailVerified', 'actions'];
  dataSource: MatTableDataSource<UserManagement>;
  isLoading = false;

  constructor(
    private userService: UserService,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    public authService: AuthService
  ) {
    this.dataSource = new MatTableDataSource<UserManagement>();
  }

  ngOnInit(): void {
    this.loadUsers();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  loadUsers(): void {
    this.isLoading = true;
    this.userService.getUsers().subscribe({
      next: (users) => {
        this.dataSource.data = users;
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Không thể tải danh sách người dùng', 'Đóng', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  addUser(): void {
    this.router.navigate(['/users/add']);
  }

  editUser(id: number): void {
    this.router.navigate(['/users/edit', id]);
  }

  toggleUserStatus(user: UserManagement): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: user.isActive ? 'Khóa tài khoản' : 'Mở khóa tài khoản',
        message: `Bạn có chắc chắn muốn ${user.isActive ? 'khóa' : 'mở khóa'} tài khoản của ${user.fullName}?`,
        confirmText: 'Xác nhận',
        cancelText: 'Hủy'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.userService.toggleUserStatus(user.id).subscribe({
          next: () => {
            this.snackBar.open(
              `${user.isActive ? 'Khóa' : 'Mở khóa'} tài khoản thành công`,
              'Đóng',
              { duration: 3000 }
            );
            this.loadUsers();
          },
          error: (error) => {
            this.snackBar.open(
              error.error?.message || `Không thể ${user.isActive ? 'khóa' : 'mở khóa'} tài khoản`,
              'Đóng',
              { duration: 3000 }
            );
          }
        });
      }
    });
  }

  deleteUser(user: UserManagement): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Xóa người dùng',
        message: `Bạn có chắc chắn muốn xóa người dùng ${user.fullName}?`,
        confirmText: 'Xóa',
        cancelText: 'Hủy'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.userService.deleteUser(user.id).subscribe({
          next: () => {
            this.snackBar.open('Xóa người dùng thành công', 'Đóng', { duration: 3000 });
            this.loadUsers();
          },
          error: (error) => {
            this.snackBar.open(
              error.error?.message || 'Không thể xóa người dùng',
              'Đóng',
              { duration: 3000 }
            );
          }
        });
      }
    });
  }

  forceVerifyEmail(user: UserManagement): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Xác thực email',
        message: `Bạn có chắc chắn muốn xác thực email cho người dùng ${user.fullName}?`,
        confirmText: 'Xác thực',
        cancelText: 'Hủy'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.userService.forceVerifyEmail(user.id).subscribe({
          next: (response) => {
            this.snackBar.open(response.message, 'Đóng', { duration: 3000 });
            this.loadUsers();
          },
          error: (error) => {
            this.snackBar.open(
              error.error?.message || 'Không thể xác thực email',
              'Đóng',
              { duration: 3000 }
            );
          }
        });
      }
    });
  }

  getRoleColor(role: UserRole): string {
    switch (role) {
      case UserRole.Admin:
        return 'warn';
      case UserRole.Librarian:
        return 'primary';
      case UserRole.Assistant:
        return 'accent';
      default:
        return 'primary';
    }
  }

  getVerificationTooltip(user: UserManagement): string {
    if (!user.emailVerified) return '';

    const method = user.emailVerificationMethod === 'Token' ? 'Xác thực qua email' : 'Xác thực bởi Admin';
    const verifier = user.emailVerifiedByName ? `Xác thực bởi: ${user.emailVerifiedByName}` : '';
    const time = user.emailVerifiedAt ? `\nThời gian: ${new Date(user.emailVerifiedAt).toLocaleString()}` : '';

    return `${method}\n${verifier}${time}`;
  }
} 