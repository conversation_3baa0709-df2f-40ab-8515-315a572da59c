# Hệ thống Quản lý Thư viện

Ứng dụng web quản lý thư viện được xây dựng với .NET backend, Angular frontend và SQL Server database.

## 🏗️ Kiến trúc Hệ thống

### Backend (.NET 8)
- **LibraryManagement.API**: Web API layer
- **LibraryManagement.Application**: Business logic layer
- **LibraryManagement.Core**: Domain models và interfaces
- **LibraryManagement.Infrastructure**: Data access layer

### Frontend (Angular 18)
- Sử dụng Angular Material cho UI components
- Standalone components architecture
- Responsive design

### Database (SQL Server)
- Entity Framework Core với Code First approach
- Clean Architecture pattern
- Repository và Unit of Work patterns

## 🚀 Các Tính Năng

### ✅ Đã Hoàn Thành
- **Quản lý Sách**: Th<PERSON><PERSON>, sử<PERSON>, xóa, tìm kiếm sách
- **Quản lý Thể loại**: <PERSON>ân loại sách theo thể loại
- **Cấu trúc Database**: Thiết kế database hoàn chỉnh với relationships
- **API Backend**: RESTful API với Swagger documentation
- **Frontend UI**: Giao diện hiện đại với Angular Material

### 🔄 Đang Phát Triển
- **Quản lý Thành viên**: CRUD operations cho thành viên thư viện
- **Quản lý Mượn/Trả**: Xử lý việc mượn và trả sách
- **Báo cáo Thống kê**: Dashboard với các báo cáo
- **Authentication**: Đăng nhập và phân quyền người dùng

## 📁 Cấu Trúc Dự Án

```
Library-management-app/
├── backend/
│   ├── LibraryManagement.API/          # Web API
│   ├── LibraryManagement.Application/  # Business Logic
│   ├── LibraryManagement.Core/         # Domain Models
│   ├── LibraryManagement.Infrastructure/ # Data Access
│   └── LibraryManagement.sln           # Solution file
├── frontend/
│   ├── src/
│   │   ├── app/
│   │   │   ├── components/            # Angular Components
│   │   │   │   ├── components/            # Angular Components
│   │   │   │   ├── models/               # TypeScript Models
│   │   │   │   ├── services/             # Angular Services
│   │   │   │   └── app.component.ts      # Main App Component
│   │   │   │
│   │   │   ├── environments/             # Environment configs
│   │   │   └── main.ts                   # Bootstrap file
│   │   │
│   │   ├── package.json                  # NPM dependencies
│   │   └── angular.json                  # Angular configuration
│   │
│   ├── database/
│   │   └── CreateDatabase.sql            # Database setup script
│   └── README.md                         # Documentation
```

## 🛠️ Cài Đặt và Chạy Ứng Dụng

### Yêu Cầu Hệ Thống
- .NET 8 SDK
- Node.js 18+ và npm
- SQL Server hoặc SQL Server LocalDB
- Angular CLI 18+

### 1. Setup Database
```sql
-- Chạy script database/CreateDatabase.sql trong SQL Server Management Studio
-- hoặc sử dụng Entity Framework migrations:
cd backend/LibraryManagement.API
dotnet ef database update
```

### 2. Chạy Backend API
```bash
cd backend/LibraryManagement.API
dotnet restore
dotnet run
```
API sẽ chạy tại: `https://localhost:7001/api/...`
Swagger UI: `https://localhost:7001/swagger`

### 3. Chạy Frontend
```bash
cd frontend
yarn install
npm start
```
Angular app sẽ chạy tại: `http://localhost:4200`

## 🎯 API Endpoints

### Books API
- `GET /api/books` - Lấy danh sách tất cả sách
- `GET /api/books/{id}` - Lấy thông tin sách theo ID
- `POST /api/books` - Thêm sách mới
- `PUT /api/books/{id}` - Cập nhật thông tin sách
- `DELETE /api/books/{id}` - Xóa sách
- `GET /api/books/search` - Tìm kiếm sách

### Categories API (Sẽ được thêm)
- `GET /api/categories` - Lấy danh sách thể loại
- `POST /api/categories` - Thêm thể loại mới

### Members API (Sẽ được thêm)
- `GET /api/members` - Lấy danh sách thành viên
- `POST /api/members` - Thêm thành viên mới

## 🎨 Giao Diện Người Dùng

### Màn Hình Chính
- **Dashboard**: Sidebar navigation với menu chính
- **Quản lý Sách**: Danh sách sách với tìm kiếm và bộ lọc
- **Form Sách**: Thêm/sửa thông tin sách
- **Quản lý Thành viên**: CRUD operations cho thành viên
- **Quản lý Mượn/Trả**: Theo dõi việc mượn và trả sách

### Tính Năng UI
- Responsive design cho mobile và desktop
- Material Design components
- Real-time search và filtering
- Confirmation dialogs cho các thao tác nguy hiểm
- Toast notifications cho feedback

## 🏗️ Kiến Trúc Kỹ Thuật

### Backend Architecture
```
┌─────────────────┐
│   API Layer     │ ← Controllers, Middleware
├─────────────────┤
│ Application     │ ← Business Logic, DTOs
├─────────────────┤
│   Core/Domain   │ ← Entities, Enums, Interfaces
├─────────────────┤
│ Infrastructure  │ ← Data Access, Repositories
└─────────────────┘
```

### Database Schema
```sql
Categories (1) ←→ (N) Books (1) ←→ (N) BorrowRecords (N) ←→ (1) Members
                                                            ↑
                                                      (N) ←→ (1) Users
```

## 🔧 Công Nghệ Sử Dụng

### Backend
- **.NET 8**: Framework chính
- **Entity Framework Core**: ORM
- **SQL Server**: Database
- **AutoMapper**: Object mapping
- **Swagger**: API documentation

### Frontend
- **Angular 18**: Framework frontend
- **Angular Material**: UI component library
- **RxJS**: Reactive programming
- **TypeScript**: Programming language
- **SCSS**: Styling

## 🚀 Triển Khai

### Development
- Backend: `dotnet run` (Port 7001)
- Frontend: `ng serve` (Port 4200)
- Database: SQL Server LocalDB

### Production
- Backend: Deploy to IIS/Azure App Service
- Frontend: Build và deploy static files
- Database: SQL Server hoặc Azure SQL Database

## 🤝 Đóng Góp

1. Fork repository
2. Tạo feature branch: `git checkout -b feature/ten-tinh-nang`
3. Commit changes: `git commit -m 'Thêm tính năng mới'`
4. Push to branch: `git push origin feature/ten-tinh-nang`
5. Tạo Pull Request

## 📝 License

Dự án này được phát hành dưới MIT License.

## 👥 Tác Giả

- **Backend Developer**: .NET Core API với Clean Architecture
- **Frontend Developer**: Angular với Material Design
- **Database Designer**: SQL Server với Entity Framework

## 📞 Liên Hệ

Nếu có thắc mắc hoặc góp ý, vui lòng tạo issue trên GitHub repository.

---

**Cảm ơn bạn đã sử dụng Hệ thống Quản lý Thư viện!** 📚 