// Base category interface
export interface BaseCategory {
  name: string;
  description?: string;
}

// Full category with all properties (for display)
export interface Category extends BaseCategory {
  id: number;
  bookCount: number;
  createdAt: Date;
  updatedAt?: Date;
  createdBy?: string;
  updatedBy?: string;
}

// For creating new category
export interface CreateCategory extends BaseCategory {
  // Inherits all BaseCategory properties
}

// For updating existing category
export interface UpdateCategory extends BaseCategory {
  id: number;
}

// Category statistics interface
export interface CategoryStatistics {
  id: number;
  name: string;
  description?: string;
  totalBooks: number;
  availableBooks: number;
  borrowedBooks: number;
  booksOnShelves: number;
  booksInStorage: number;
  createdAt: Date;
  updatedAt?: Date;
  popularBooks: PopularBook[];
}

export interface PopularBook {
  bookId: number;
  title: string;
  author: string;
  borrowCount: number;
  categoryName: string;
}

// Category validation result
export interface CategoryValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Search interfaces
export interface CategorySearchParams {
  query?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

export interface CategorySearchResult {
  categories: Category[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
