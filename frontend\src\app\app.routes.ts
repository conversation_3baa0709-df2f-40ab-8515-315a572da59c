import { Routes } from "@angular/router";
import { authGuard } from "./guards/auth.guard";
import {
  bookManagementGuard,
  memberManagementGuard,
  borrowOperationsGuard,
  staffGuard,
  adminGuard,
} from "./guards/role.guard";

export const routes: Routes = [
  { path: "", redirectTo: "/dashboard", pathMatch: "full" },
  {
    path: "login",
    loadComponent: () =>
      import("./components/login/login.component").then(
        (m) => m.LoginComponent
      ),
  },
  {
    path: "register",
    loadComponent: () =>
      import("./components/register/register.component").then(
        (m) => m.RegisterComponent
      ),
  },
  {
    path: "verify-email",
    loadComponent: () =>
      import("./components/verify-email/verify-email.component").then(
        (m) => m.VerifyEmailComponent
      ),
  },
  {
    path: "dashboard",
    loadComponent: () =>
      import("./components/dashboard/dashboard.component").then(
        (m) => m.DashboardComponent
      ),
    canActivate: [staffGuard], // All staff can view dashboard
  },
  {
    path: "reports",
    children: [
      {
        path: "overdue-books",
        loadComponent: () =>
          import(
            "./components/reports/overdue-books/overdue-books.component"
          ).then((m) => m.OverdueBooksComponent),
        canActivate: [borrowOperationsGuard],
      },
      {
        path: "fine-collection",
        loadComponent: () =>
          import(
            "./components/reports/fine-collection/fine-collection.component"
          ).then((m) => m.FineCollectionComponent),
        canActivate: [borrowOperationsGuard],
      },
      {
        path: "custom",
        loadComponent: () =>
          import(
            "./components/reports/custom-report/custom-report.component"
          ).then((m) => m.CustomReportComponent),
        canActivate: [borrowOperationsGuard],
      },
    ],
    canActivate: [borrowOperationsGuard], // All staff can access reports
  },
  {
    path: "books",
    loadComponent: () =>
      import("./components/book-list/book-list.component").then(
        (m) => m.BookListComponent
      ),
    canActivate: [staffGuard], // All staff can view books
  },
  {
    path: "books/add",
    loadComponent: () =>
      import("./components/book-form/book-form.component").then(
        (m) => m.BookFormComponent
      ),
    canActivate: [bookManagementGuard], // Only Admin and Librarian can add books
  },
  {
    path: "books/edit/:id",
    loadComponent: () =>
      import("./components/book-form/book-form.component").then(
        (m) => m.BookFormComponent
      ),
    canActivate: [bookManagementGuard], // Only Admin and Librarian can edit books
  },
  {
    path: "members",
    loadComponent: () =>
      import("./components/member-list/member-list.component").then(
        (m) => m.MemberListComponent
      ),
    canActivate: [memberManagementGuard], // Only Admin and Librarian can manage members
  },
  {
    path: "members/add",
    loadComponent: () =>
      import("./components/member-form/member-form.component").then(
        (m) => m.MemberFormComponent
      ),
    canActivate: [memberManagementGuard], // Only Admin and Librarian can add members
  },
  {
    path: "members/edit/:id",
    loadComponent: () =>
      import("./components/member-form/member-form.component").then(
        (m) => m.MemberFormComponent
      ),
    canActivate: [memberManagementGuard], // Only Admin and Librarian can edit members
  },
  {
    path: "borrows",
    loadComponent: () =>
      import("./components/borrow-list/borrow-list.component").then(
        (m) => m.BorrowListComponent
      ),
    canActivate: [borrowOperationsGuard], // All staff can handle borrow operations
  },
  {
    path: "users",
    loadComponent: () =>
      import("./components/user-list/user-list.component").then(
        (m) => m.UserListComponent
      ),
    canActivate: [adminGuard], // Only Admin can manage users
  },
  {
    path: "users/add",
    loadComponent: () =>
      import("./components/user-form/user-form.component").then(
        (m) => m.UserFormComponent
      ),
    canActivate: [adminGuard], // Only Admin can add users
  },
  {
    path: "users/edit/:id",
    loadComponent: () =>
      import("./components/user-form/user-form.component").then(
        (m) => m.UserFormComponent
      ),
    canActivate: [adminGuard], // Only Admin can edit users
  },
  {
    path: "shelves",
    loadComponent: () =>
      import("./components/shelves-list/shelves-list.component").then(
        (m) => m.ShelvesListComponent
      ),
    canActivate: [staffGuard], // All staff can view shelves
  },
  {
    path: "shelves/add",
    loadComponent: () =>
      import("./components/shelf-form/shelf-form.component").then(
        (m) => m.ShelfFormComponent
      ),
    canActivate: [staffGuard], // All staff can add shelves
  },
  {
    path: "shelves/edit/:id",
    loadComponent: () =>
      import("./components/shelf-form/shelf-form.component").then(
        (m) => m.ShelfFormComponent
      ),
    canActivate: [staffGuard], // All staff can edit shelves
  },
  {
    path: "shelves/:id",
    loadComponent: () =>
      import("./components/shelf-detail/shelf-detail.component").then(
        (m) => m.ShelfDetailComponent
      ),
    canActivate: [staffGuard], // All staff can view shelf details
  },
  {
    path: "shelves/:id/books",
    loadComponent: () =>
      import("./components/shelf-books/shelf-books.component").then(
        (m) => m.ShelfBooksComponent
      ),
    canActivate: [staffGuard], // All staff can manage books in shelves
  },

  // Category Management Routes
  {
    path: "categories",
    loadComponent: () =>
      import("./components/category-list/category-list.component").then(
        (m) => m.CategoryListComponent
      ),
    canActivate: [staffGuard], // All staff can view categories
  },
  {
    path: "categories/add",
    loadComponent: () =>
      import("./components/category-form/category-form.component").then(
        (m) => m.CategoryFormComponent
      ),
    canActivate: [staffGuard], // All staff can add categories
  },
  {
    path: "categories/edit/:id",
    loadComponent: () =>
      import("./components/category-form/category-form.component").then(
        (m) => m.CategoryFormComponent
      ),
    canActivate: [staffGuard], // All staff can edit categories
  },
  {
    path: "categories/:id",
    loadComponent: () =>
      import("./components/category-detail/category-detail.component").then(
        (m) => m.CategoryDetailComponent
      ),
    canActivate: [staffGuard], // All staff can view category details
  },
  {
    path: "zones",
    loadComponent: () =>
      import("./components/zone-list/zone-list.component").then(
        (m) => m.ZoneListComponent
      ),
    canActivate: [staffGuard], // All staff can view shelves
  },
  { path: "**", redirectTo: "/shelves" },
];
