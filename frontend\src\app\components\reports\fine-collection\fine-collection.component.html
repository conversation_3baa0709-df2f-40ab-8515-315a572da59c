<div class="fine-collection-container">
  <div class="header-section">
    <h1 class="page-title"><PERSON><PERSON>o c<PERSON>o thu phí <PERSON>h<PERSON>t</h1>
    <div class="actions">
      <button mat-raised-button color="primary" (click)="exportToCsv()" matTooltip="Xuất báo cáo dạng CSV">
        <mat-icon>file_download</mat-icon> Xuất CSV
      </button>
      <button mat-raised-button color="accent" (click)="printReport()" matTooltip="In báo cáo">
        <mat-icon>print</mat-icon> In báo cáo
      </button>
      <button mat-raised-button (click)="loadFineCollection()" matTooltip="Làm mới dữ liệu">
        <mat-icon>refresh</mat-icon> Làm mới
      </button>
    </div>
  </div>

  <mat-card class="date-filter-card">
    <mat-card-content>
      <div class="date-filter-container">
        <mat-form-field appearance="outline">
          <mat-label>Từ ngày</mat-label>
          <input matInput [matDatepicker]="startPicker" [(ngModel)]="startDate">
          <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
          <mat-datepicker #startPicker></mat-datepicker>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Đến ngày</mat-label>
          <input matInput [matDatepicker]="endPicker" [(ngModel)]="endDate">
          <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
          <mat-datepicker #endPicker></mat-datepicker>
        </mat-form-field>

        <div class="date-filter-actions">
          <button mat-raised-button color="primary" (click)="applyDateFilter()">
            <mat-icon>filter_alt</mat-icon> Lọc
          </button>
          <button mat-button (click)="resetDateFilter()">
            <mat-icon>refresh</mat-icon> Đặt lại
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <mat-card>
    <mat-card-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-progress-bar mode="indeterminate"></mat-progress-bar>
        <p>Đang tải dữ liệu...</p>
      </div>

      <div *ngIf="error" class="error-container">
        <p>{{ error }}</p>
        <button mat-raised-button color="primary" (click)="loadFineCollection()">Thử lại</button>
      </div>

      <div *ngIf="!isLoading && !error && fineReport" class="report-container">
        <div class="summary-card">
          <div class="summary-item">
            <div class="summary-label">Tổng số phiếu phạt</div>
            <div class="summary-value">{{ fineReport.totalRecords }}</div>
          </div>
          <div class="summary-item">
            <div class="summary-label">Tổng phí phạt</div>
            <div class="summary-value">{{ fineReport.totalFineCollected | currency:'VND':'symbol':'1.0-0' }}</div>
          </div>
          <div class="summary-item">
            <div class="summary-label">Phí phạt trung bình</div>
            <div class="summary-value">{{ fineReport.averageFine | currency:'VND':'symbol':'1.0-0' }}</div>
          </div>
          <div class="summary-item">
            <div class="summary-label">Phí phạt cao nhất</div>
            <div class="summary-value">{{ fineReport.maxFine | currency:'VND':'symbol':'1.0-0' }}</div>
          </div>
        </div>

        <div class="filter-container">
          <mat-form-field appearance="outline">
            <mat-label>Tìm kiếm</mat-label>
            <input matInput (keyup)="applyFilter($event)" [(ngModel)]="filterValue" placeholder="Tìm theo ID mượn, ID sách hoặc ID thành viên">
            <button *ngIf="filterValue" matSuffix mat-icon-button aria-label="Clear" (click)="clearFilter()">
              <mat-icon>close</mat-icon>
            </button>
            <mat-icon matPrefix>search</mat-icon>
          </mat-form-field>
        </div>

        <div class="table-container">
          <table mat-table [dataSource]="filteredData" matSort class="mat-elevation-z2">
            <!-- Borrow ID Column -->
            <ng-container matColumnDef="borrowId">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> ID mượn </th>
              <td mat-cell *matCellDef="let detail"> {{ detail.borrowId }} </td>
            </ng-container>

            <!-- Book Info Column -->
            <ng-container matColumnDef="bookInfo">
              <th mat-header-cell *matHeaderCellDef> Sách </th>
              <td mat-cell *matCellDef="let detail"> ID: {{ detail.bookId }} </td>
            </ng-container>

            <!-- Member Info Column -->
            <ng-container matColumnDef="memberInfo">
              <th mat-header-cell *matHeaderCellDef> Thành viên </th>
              <td mat-cell *matCellDef="let detail"> ID: {{ detail.memberId }} </td>
            </ng-container>

            <!-- Return Date Column -->
            <ng-container matColumnDef="returnDate">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Ngày trả </th>
              <td mat-cell *matCellDef="let detail"> {{ detail.returnDate | date:'dd/MM/yyyy' }} </td>
            </ng-container>

            <!-- Days Overdue Column -->
            <ng-container matColumnDef="daysOverdue">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Số ngày quá hạn </th>
              <td mat-cell *matCellDef="let detail" class="overdue-days"> {{ detail.daysOverdue }} </td>
            </ng-container>

            <!-- Fine Column -->
            <ng-container matColumnDef="fine">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Phí phạt </th>
              <td mat-cell *matCellDef="let detail" class="fine-amount"> {{ detail.fine | currency:'VND':'symbol':'1.0-0' }} </td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef> Thao tác </th>
              <td mat-cell *matCellDef="let detail">
                <button mat-icon-button color="primary" [routerLink]="['/borrows', detail.borrowId]" matTooltip="Xem chi tiết">
                  <mat-icon>visibility</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

            <!-- Row shown when there is no matching data. -->
            <tr class="mat-row" *matNoDataRow>
              <td class="mat-cell" colspan="7">
                <div class="no-data-message">
                  <mat-icon>search_off</mat-icon>
                  <p>Không tìm thấy dữ liệu phù hợp với "{{ filterValue }}"</p>
                </div>
              </td>
            </tr>
          </table>

          <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>