export interface Book {
  id: number;
  title: string;
  author: string;
  isbn?: string;
  publisher?: string;
  publishedDate?: Date;
  categoryId: number;
  categoryName: string;
  quantity: number; // Tổng số lượng
  stockQuantity: number; // Số lượng trong kho
  onShelfQuantity: number; // Số lượng trên kệ

  totalQuantity: number; // Tổng thực tế = stockQuantity + onShelfQuantity
  description?: string;
  imageUrl?: string;
  price?: number;
  createdAt: Date;
  updatedAt?: Date;
  // Simplified shelf location
  bookshelfId?: number;
  bookshelfName?: string;
  locationCode?: string;
  location?: string;
}

export interface CreateBook {
  title: string;
  author: string;
  isbn?: string;
  publisher?: string;
  publishedDate?: Date;
  categoryId: number;
  quantity: number;
  description?: string;
  imageUrl?: string;
  price?: number;
  bookshelfId?: number;
  locationCode?: string;
}

export interface UpdateBook {
  title: string;
  author: string;
  isbn?: string;
  publisher?: string;
  publishedDate?: Date;
  categoryId: number;
  quantity: number;
  description?: string;
  imageUrl?: string;
  price?: number;
  bookshelfId?: number;
  locationCode?: string;
}

export interface BookOnShelf {
  id: number;
  title: string;
  author: string;
  isbn: string;
  stockQuantity: number;
  location: string;
}
