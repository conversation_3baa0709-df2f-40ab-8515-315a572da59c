-- <PERSON>ript to create new Admin account if not exists
USE LibraryManagementDb;
GO

-- Delete existing admin if exists
DELETE FROM Users WHERE Email = '<EMAIL>';

-- Create new admin account
INSERT INTO Users (<PERSON>rname, Email, PasswordHash, FirstName, LastName, Role, IsActive, EmailVerified, CreatedAt)
VALUES (
    'admin',
    '<EMAIL>', 
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- BCrypt hash for "password"
    N'Admin',
    N'System',
    1, -- Admin role
    1, -- Active
    1, -- Email verified
    GETUTCDATE()
);

-- <PERSON><PERSON><PERSON> created admin
SELECT 
    Id,
    Username,
    Email,
    FirstName,
    LastName,
    Role,
    IsActive,
    EmailVerified,
    CreatedAt
FROM Users 
WHERE Email = '<EMAIL>';

PRINT 'New admin account created successfully!';
PRINT 'Login credentials:';
PRINT 'Email: <EMAIL>';
PRINT 'Password: password'; 