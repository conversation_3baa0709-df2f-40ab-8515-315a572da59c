using System.Net;
using System.Net.Mail;
using LibraryManagement.Core.Interfaces;
using Microsoft.Extensions.Configuration;

namespace LibraryManagement.Infrastructure.Services;

public class EmailService : IEmailService
{
    private readonly IConfiguration _configuration;
    private readonly string _fromEmail;
    private readonly string _fromName;
    private readonly string _smtpHost;
    private readonly int _smtpPort;
    private readonly string _smtpUsername;
    private readonly string _smtpPassword;

    public EmailService(IConfiguration configuration)
    {
        _configuration = configuration;
        _fromEmail = _configuration["Email:FromEmail"] ?? "<EMAIL>";
        _fromName = _configuration["Email:FromName"] ?? "Hệ thống Quản lý Thư viện";
        _smtpHost = _configuration["Email:SmtpHost"] ?? "smtp.gmail.com";
        _smtpPort = int.Parse(_configuration["Email:SmtpPort"] ?? "587");
        _smtpUsername = _configuration["Email:SmtpUsername"] ?? "";
        _smtpPassword = _configuration["Email:SmtpPassword"] ?? "";
    }

    public async Task SendEmailVerificationAsync(string email, string firstName, string verificationToken)
    {
        var subject = "Xác nhận tài khoản - Hệ thống Quản lý Thư viện";
        var verificationUrl = $"{_configuration["Frontend:BaseUrl"]}/verify-email?token={verificationToken}";
        
        var body = $@"
            <html>
            <body style='font-family: Arial, sans-serif;'>
                <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                    <h2 style='color: #3f51b5;'>Chào {firstName},</h2>
                    <p>Cảm ơn bạn đã đăng ký tài khoản tại Hệ thống Quản lý Thư viện!</p>
                    <p>Để hoàn tất việc đăng ký, vui lòng nhấp vào liên kết bên dưới để xác nhận email của bạn:</p>
                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='{verificationUrl}' 
                           style='background-color: #3f51b5; color: white; padding: 12px 30px; 
                                  text-decoration: none; border-radius: 5px; display: inline-block;'>
                            Xác nhận Email
                        </a>
                    </div>
                    <p style='color: #666; font-size: 14px;'>
                        Nếu bạn không thể nhấp vào nút trên, hãy copy và paste link sau vào trình duyệt:<br>
                        <a href='{verificationUrl}'>{verificationUrl}</a>
                    </p>
                    <p style='color: #666; font-size: 14px;'>
                        Link này sẽ hết hạn sau 24 giờ.
                    </p>
                    <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
                    <p style='color: #999; font-size: 12px;'>
                        Đây là email tự động, vui lòng không trả lời email này.
                    </p>
                </div>
            </body>
            </html>";

        await SendEmailAsync(email, subject, body);
    }

    public async Task SendPasswordResetAsync(string email, string firstName, string resetToken)
    {
        var subject = "Đặt lại mật khẩu - Hệ thống Quản lý Thư viện";
        var resetUrl = $"{_configuration["Frontend:BaseUrl"]}/reset-password?token={resetToken}";
        
        var body = $@"
            <html>
            <body style='font-family: Arial, sans-serif;'>
                <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                    <h2 style='color: #f44336;'>Chào {firstName},</h2>
                    <p>Bạn đã yêu cầu đặt lại mật khẩu cho tài khoản của mình.</p>
                    <p>Nhấp vào liên kết bên dưới để đặt lại mật khẩu:</p>
                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='{resetUrl}' 
                           style='background-color: #f44336; color: white; padding: 12px 30px; 
                                  text-decoration: none; border-radius: 5px; display: inline-block;'>
                            Đặt lại mật khẩu
                        </a>
                    </div>
                    <p style='color: #666; font-size: 14px;'>
                        Link này sẽ hết hạn sau 1 giờ.
                    </p>
                    <p style='color: #f44336; font-size: 14px;'>
                        <strong>Lưu ý:</strong> Nếu bạn không yêu cầu đặt lại mật khẩu, 
                        vui lòng bỏ qua email này.
                    </p>
                </div>
            </body>
            </html>";

        await SendEmailAsync(email, subject, body);
    }

    public async Task SendWelcomeEmailAsync(string email, string firstName)
    {
        var subject = "Chào mừng đến với Hệ thống Quản lý Thư viện!";
        
        var body = $@"
            <html>
            <body style='font-family: Arial, sans-serif;'>
                <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                    <h2 style='color: #4caf50;'>Chào mừng {firstName}!</h2>
                    <p>Tài khoản của bạn đã được xác nhận thành công!</p>
                    <p>Bạn có thể bắt đầu sử dụng hệ thống để:</p>
                    <ul>
                        <li>📚 Quản lý sách</li>
                        <li>👥 Quản lý thành viên</li>
                        <li>📖 Quản lý mượn/trả sách</li>
                    </ul>
                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='{_configuration["Frontend:BaseUrl"]}' 
                           style='background-color: #4caf50; color: white; padding: 12px 30px; 
                                  text-decoration: none; border-radius: 5px; display: inline-block;'>
                            Bắt đầu sử dụng
                        </a>
                    </div>
                </div>
            </body>
            </html>";

        await SendEmailAsync(email, subject, body);
    }

    public async Task SendEmailVerificationConfirmationAsync(string email, string firstName, string verifiedByName, string verificationMethod)
    {
        var subject = "Email đã được xác thực - Hệ thống Quản lý Thư viện";
        
        var body = $@"
            <html>
            <body style='font-family: Arial, sans-serif;'>
                <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                    <h2 style='color: #4caf50;'>Chào {firstName},</h2>
                    <p>Email của bạn đã được xác thực thành công!</p>
                    <p>Thông tin xác thực:</p>
                    <ul>
                        <li>Xác thực bởi: {verifiedByName}</li>
                        <li>Phương thức: {verificationMethod}</li>
                        <li>Thời gian: {DateTime.UtcNow:dd/MM/yyyy HH:mm:ss} UTC</li>
                    </ul>
                    <p>Tài khoản của bạn đã được kích hoạt và bạn có thể đăng nhập ngay bây giờ.</p>
                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='{_configuration["Frontend:BaseUrl"]}/login' 
                           style='background-color: #4caf50; color: white; padding: 12px 30px; 
                                  text-decoration: none; border-radius: 5px; display: inline-block;'>
                            Đăng nhập
                        </a>
                    </div>
                </div>
            </body>
            </html>";

        await SendEmailAsync(email, subject, body);
    }

    private async Task SendEmailAsync(string toEmail, string subject, string body)
    {
        try
        {
            using var client = new SmtpClient(_smtpHost, _smtpPort);
            client.UseDefaultCredentials = false;
            client.Credentials = new NetworkCredential(_smtpUsername, _smtpPassword);
            client.EnableSsl = true;

            var mailMessage = new MailMessage
            {
                From = new MailAddress(_fromEmail, _fromName),
                Subject = subject,
                Body = body,
                IsBodyHtml = true
            };

            mailMessage.To.Add(toEmail);
            await client.SendMailAsync(mailMessage);
        }
        catch (Exception ex)
        {
            // Log error (implement proper logging)
            throw new InvalidOperationException($"Failed to send email: {ex.Message}", ex);
        }
    }
} 