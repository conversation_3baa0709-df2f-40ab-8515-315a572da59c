import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface BorrowRecord {
  id: number;
  bookId: number;
  bookTitle: string;
  bookAuthor: string;
  memberId: number;
  memberName: string;
  borrowDate: string;
  dueDate: string;
  returnDate?: string;
  status: BorrowStatus;
  statusName: string;
  notes?: string;
  fine?: number;
  isOverdue: boolean;
  daysOverdue: number;
  createdAt: string;
}

export interface CreateBorrowRequest {
  bookId: number;
  memberId: number;
  dueDate: string;
  notes?: string;
}

export interface ReturnBookRequest {
  borrowRecordId: number;
  returnDate: string;
  fine?: number;
  notes?: string;
}

export enum BorrowStatus {
  Borrowed = 1,
  Returned = 2,
  Overdue = 3,
  Lost = 4,
  Renewed = 5
}

export interface BorrowSearchParams {
  memberName?: string;
  bookTitle?: string;
  status?: BorrowStatus;
  overdue?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class BorrowService {
  private apiUrl = `${environment.apiUrl}/borrows`;

  constructor(private http: HttpClient) { }

  // Get all borrow records
  getBorrowRecords(): Observable<BorrowRecord[]> {
    return this.http.get<BorrowRecord[]>(this.apiUrl);
  }

  // Get a specific borrow record by ID
  getBorrowRecord(id: number): Observable<BorrowRecord> {
    return this.http.get<BorrowRecord>(`${this.apiUrl}/${id}`);
  }

  // Create a new borrow record (borrow a book)
  borrowBook(request: CreateBorrowRequest): Observable<BorrowRecord> {
    return this.http.post<BorrowRecord>(this.apiUrl, request);
  }

  // Return a book
  returnBook(id: number, request: ReturnBookRequest): Observable<BorrowRecord> {
    return this.http.put<BorrowRecord>(`${this.apiUrl}/${id}/return`, request);
  }

  // Get overdue books
  getOverdueBooks(): Observable<BorrowRecord[]> {
    return this.http.get<BorrowRecord[]>(`${this.apiUrl}/overdue`);
  }

  // Search borrow records
  searchBorrowRecords(params: BorrowSearchParams): Observable<BorrowRecord[]> {
    let httpParams = new HttpParams();
    
    if (params.memberName) {
      httpParams = httpParams.set('memberName', params.memberName);
    }
    if (params.bookTitle) {
      httpParams = httpParams.set('bookTitle', params.bookTitle);
    }
    if (params.status !== undefined) {
      httpParams = httpParams.set('status', params.status.toString());
    }
    if (params.overdue !== undefined) {
      httpParams = httpParams.set('overdue', params.overdue.toString());
    }

    return this.http.get<BorrowRecord[]>(`${this.apiUrl}/search`, { params: httpParams });
  }

  renewBook(id: number): Observable<BorrowRecord> {
    return this.http.post<BorrowRecord>(`${this.apiUrl}/${id}/renew`, {});
  }

  // Helper method to get status name in Vietnamese
  getStatusDisplayName(status: BorrowStatus): string {
    switch (status) {
      case BorrowStatus.Borrowed:
        return 'Đang mượn';
      case BorrowStatus.Returned:
        return 'Đã trả';
      case BorrowStatus.Overdue:
        return 'Quá hạn';
      case BorrowStatus.Lost:
        return 'Mất sách';
      case BorrowStatus.Renewed:
        return 'Gia hạn';
      default:
        return 'Không xác định';
    }
  }

  // Helper method to get status color class
  getStatusColorClass(status: BorrowStatus): string {
    switch (status) {
      case BorrowStatus.Borrowed:
        return 'status-borrowed';
      case BorrowStatus.Returned:
        return 'status-returned';
      case BorrowStatus.Overdue:
        return 'status-overdue';
      case BorrowStatus.Lost:
        return 'status-lost';
      case BorrowStatus.Renewed:
        return 'status-renewed';
      default:
        return '';
    }
  }
} 