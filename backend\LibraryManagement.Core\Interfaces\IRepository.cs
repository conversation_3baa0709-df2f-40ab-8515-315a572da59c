using System.Linq.Expressions;

namespace LibraryManagement.Core.Interfaces;

public interface IRepository<T> where T : class
{
    void Update(T entity);
    void Remove(T entity);


    Task<T?> GetByIdAsync(int id);
    Task<IEnumerable<T>> GetAllAsync();
    IQueryable<T> Query();
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);
    Task<T> AddAsync(T entity);
    Task UpdateAsync(T entity);
    Task DeleteAsync(T entity);
    Task<bool> ExistsAsync(int id);
    Task<int> CountAsync();
    Task AddRangeAsync(IEnumerable<T> entities);

}