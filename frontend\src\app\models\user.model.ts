export enum UserRole {
  Admin = 1,
  Librarian = 2,
  Assistant = 3
}

export interface BaseUser {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
}

export interface User extends BaseUser {
  role: UserRole;
}

export interface UserManagement extends BaseUser {
  fullName: string;
  role: UserRole;
  roleName: string;
  isActive: boolean;
  emailVerified: boolean;
  emailVerifiedAt?: Date;
  emailVerificationMethod?: 'Token' | 'AdminForce';
  emailVerifiedByName?: string;
  lastLoginDate?: Date;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreateUser {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  role: UserRole;
  isActive: boolean;
}

export interface UpdateUser {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  isActive: boolean;
}

export interface ChangePassword {
  currentPassword: string;
  newPassword: string;
  confirmNewPassword: string;
}

export const USER_ROLE_OPTIONS = [
  {
    value: UserRole.Admin,
    label: 'Quản trị viên',
    description: 'Toàn quyền quản lý hệ thống'
  },
  {
    value: UserRole.Librarian,
    label: 'Thủ thư',
    description: 'Quản lý sách, thành viên, mượn/trả sách'
  },
  {
    value: UserRole.Assistant,
    label: 'Trợ lý',
    description: 'Hỗ trợ quản lý sách và thành viên'
  }
];

export interface UserStatusOption {
  value: boolean;
  label: string;
  color: string;
}

export const USER_STATUS_OPTIONS: UserStatusOption[] = [
  { value: true, label: 'Hoạt động', color: 'primary' },
  { value: false, label: 'Bị khóa', color: 'warn' }
];

export interface UserSearchCriteria {
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  role?: UserRole;
  isActive?: boolean;
} 