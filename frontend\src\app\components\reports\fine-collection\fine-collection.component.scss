.fine-collection-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  color: #3f51b5;
  font-weight: 500;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.actions {
  display: flex;
  gap: 10px;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.error-container {
  color: #f44336;
}

.date-filter-card {
  margin-bottom: 20px;
}

.date-filter-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 16px;
}

.date-filter-container mat-form-field {
  flex: 1;
  min-width: 200px;
}

.date-filter-actions {
  display: flex;
  gap: 8px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-container mat-form-field {
  width: 100%;
}

.summary-card {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.summary-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.summary-value {
  font-size: 24px;
  font-weight: 500;
  color: #3f51b5;
}

.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
}

.mat-column-borrowId {
  width: 100px;
}

.mat-column-actions {
  width: 80px;
  text-align: center;
}

.mat-column-daysOverdue, .mat-column-fine {
  width: 120px;
}

.overdue-days {
  font-weight: 500;
  color: #f44336;
}

.fine-amount {
  font-weight: 500;
  color: #3f51b5;
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
}

.no-data-message mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  color: #ccc;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .actions {
    margin-top: 16px;
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  
  .date-filter-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .date-filter-actions {
    flex-direction: row;
    justify-content: flex-end;
  }
  
  .summary-card {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}