using System.ComponentModel.DataAnnotations;

namespace LibraryManagement.Application.DTOs;

public class CategoryDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int BookCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }
}

public class CreateCategoryDto
{
    [Required(ErrorMessage = "Tên thể loại là bắt buộc")]
    [StringLength(200, MinimumLength = 2, ErrorMessage = "Tên thể loại phải từ 2-200 ký tự")]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000, ErrorMessage = "Mô tả không được quá 1000 ký tự")]
    public string? Description { get; set; }
}

public class UpdateCategoryDto : CreateCategoryDto
{
    public int Id { get; set; }
}

public class CategoryStatisticsDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int TotalBooks { get; set; }
    public int AvailableBooks { get; set; }
    public int BorrowedBooks { get; set; }
    public int BooksOnShelves { get; set; }
    public int BooksInStorage { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    // Most popular books in this category
    public List<PopularBookDto> PopularBooks { get; set; } = new();
}

// PopularBookDto is already defined in DashboardDto.cs

public class CategoryValidationDto
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}

public class CategorySearchDto
{
    public string? Query { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; } = "Name";
    public string? SortDirection { get; set; } = "asc";
}

public class CategorySearchResultDto
{
    public List<CategoryDto> Categories { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
}
