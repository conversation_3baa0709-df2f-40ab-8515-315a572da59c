import { Component, OnInit, Inject, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Member } from '../../models/member.model';
import { BorrowRecord, BorrowStatus } from '../../models/member.model';
import { BorrowService } from '../../services/borrow.service';
import { MemberService } from '../../services/member.service';

@Component({
  selector: 'app-member-borrow-history-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatTableModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatTooltipModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSnackBarModule
  ],
  template: `
    <h2 mat-dialog-title>Lịch sử mượn sách - {{ data.member.fullName }}</h2>
    <mat-dialog-content>
      <mat-card class="member-info">
        <mat-card-content>
          <p><strong>Email:</strong> {{ data.member.email }}</p>
          <p><strong>Số điện thoại:</strong> {{ data.member.phone || 'Chưa cập nhật' }}</p>
          <p><strong>Trạng thái:</strong> 
            <mat-chip [class]="getMemberStatusClass(data.member.status)">
              {{ getMemberStatusName(data.member.status) }}
            </mat-chip>
          </p>
        </mat-card-content>
      </mat-card>

      <mat-form-field appearance="outline" class="w-100 mt-3">
        <mat-label>Tìm kiếm</mat-label>
        <input matInput (keyup)="applyFilter($event)" placeholder="Nhập từ khóa..." #input>
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>

      <div class="mat-elevation-z8">
        <table mat-table [dataSource]="dataSource" matSort>
          <!-- Book Title Column -->
          <ng-container matColumnDef="bookTitle">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Tên sách</th>
            <td mat-cell *matCellDef="let record">{{ record.bookTitle }}</td>
          </ng-container>

          <!-- Borrow Date Column -->
          <ng-container matColumnDef="borrowDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Ngày mượn</th>
            <td mat-cell *matCellDef="let record">{{ record.borrowDate | date:'dd/MM/yyyy' }}</td>
          </ng-container>

          <!-- Due Date Column -->
          <ng-container matColumnDef="dueDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Hạn trả</th>
            <td mat-cell *matCellDef="let record">
              <span [class]="getDueDateClass(record)">
                {{ record.dueDate | date:'dd/MM/yyyy' }}
              </span>
            </td>
          </ng-container>

          <!-- Return Date Column -->
          <ng-container matColumnDef="returnDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Ngày trả</th>
            <td mat-cell *matCellDef="let record">
              {{ record.returnDate ? (record.returnDate | date:'dd/MM/yyyy') : '-' }}
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Trạng thái</th>
            <td mat-cell *matCellDef="let record">
              <mat-chip [class]="getStatusColorClass(record.status)">
                {{ record.statusName }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Fine Column -->
          <ng-container matColumnDef="fine">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Tiền phạt</th>
            <td mat-cell *matCellDef="let record">
              {{ record.fine > 0 ? (record.fine | number) + ' VND' : '-' }}
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Thao tác</th>
            <td mat-cell *matCellDef="let record">
              <button mat-icon-button color="primary"
                      *ngIf="record.status === BorrowStatus.Borrowed"
                      (click)="renewBook(record)"
                      matTooltip="Gia hạn sách">
                <mat-icon>update</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

          <!-- Row shown when there is no matching data. -->
          <tr class="mat-row" *matNoDataRow>
            <td class="mat-cell" colspan="7">
              Không tìm thấy dữ liệu phù hợp với từ khóa "{{input.value}}"
            </td>
          </tr>
        </table>

        <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" 
                      aria-label="Chọn số dòng trên trang"
                      showFirstLastButtons>
        </mat-paginator>
      </div>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button mat-dialog-close>Đóng</button>
    </mat-dialog-actions>
  `,
  styles: [`
    .member-info {
      margin-bottom: 20px;
    }
    .w-100 {
      width: 100%;
    }
    .mt-3 {
      margin-top: 1rem;
    }
    .status-borrowed { background-color: #2196F3; color: white; }
    .status-returned { background-color: #4CAF50; color: white; }
    .status-overdue { background-color: #F44336; color: white; }
    .status-lost { background-color: #9C27B0; color: white; }
    .status-renewed { background-color: #FF9800; color: white; }
    .due-date-normal { color: inherit; }
    .due-date-warning { color: #FF9800; }
    .due-date-overdue { color: #F44336; }
    table {
      width: 100%;
    }
    .mat-column-fine {
      text-align: right;
      padding-right: 16px !important;
    }
    .mat-column-actions {
      width: 60px;
      text-align: center;
    }
  `]
})
export class MemberBorrowHistoryDialogComponent implements OnInit, AfterViewInit {
  displayedColumns: string[] = ['bookTitle', 'borrowDate', 'dueDate', 'returnDate', 'status', 'fine', 'actions'];
  dataSource: MatTableDataSource<BorrowRecord>;
  BorrowStatus = BorrowStatus;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    public dialogRef: MatDialogRef<MemberBorrowHistoryDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { member: Member },
    private memberService: MemberService,
    private borrowService: BorrowService,
    private snackBar: MatSnackBar
  ) {
    this.dataSource = new MatTableDataSource<BorrowRecord>([]);
  }

  ngOnInit() {
    this.loadBorrowHistory();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.dataSource.sortingDataAccessor = (item: any, property: string) => {
      switch (property) {
        case 'fine': return item.fine || 0;
        default: return item[property];
      }
    };
  }

  loadBorrowHistory() {
    this.memberService.getMemberBorrowHistory(this.data.member.id).subscribe({
      next: (records) => {
        this.dataSource.data = records;
      },
      error: (error) => {
        console.error('Error loading borrow history:', error);
        this.snackBar.open('Không thể tải lịch sử mượn sách', 'Đóng', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top'
        });
      }
    });
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  getMemberStatusClass(status: number): string {
    switch (status) {
      case 0: return 'status-active';
      case 1: return 'status-suspended';
      case 2: return 'status-expired';
      case 3: return 'status-banned';
      default: return '';
    }
  }

  getMemberStatusName(status: number): string {
    switch (status) {
      case 0: return 'Hoạt động';
      case 1: return 'Tạm khóa';
      case 2: return 'Hết hạn';
      case 3: return 'Cấm';
      default: return 'Không xác định';
    }
  }

  getDueDateClass(record: BorrowRecord): string {
    if (!record.dueDate || record.returnDate) {
      return 'due-date-normal';
    }

    const today = new Date();
    const dueDate = new Date(record.dueDate);
    const diffDays = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return 'due-date-overdue';
    } else if (diffDays <= 3) {
      return 'due-date-warning';
    }
    return 'due-date-normal';
  }

  getStatusColorClass(status: number): string {
    switch (status) {
      case 0: return 'status-borrowed';
      case 1: return 'status-returned';
      case 2: return 'status-overdue';
      case 3: return 'status-lost';
      case 4: return 'status-renewed';
      default: return '';
    }
  }

  renewBook(record: BorrowRecord) {
    this.borrowService.renewBook(record.id).subscribe({
      next: (updatedRecord) => {
        // Cập nhật record trong dataSource
        const index = this.dataSource.data.findIndex(r => r.id === record.id);
        if (index !== -1) {
          this.dataSource.data[index] = updatedRecord;
          this.dataSource._updateChangeSubscription();
        }
        
        this.snackBar.open('Gia hạn sách thành công', 'Đóng', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top'
        });
      },
      error: (error) => {
        console.error('Error renewing book:', error);
        this.snackBar.open(
          error.error || 'Không thể gia hạn sách. Vui lòng thử lại sau.',
          'Đóng',
          {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top'
          }
        );
      }
    });
  }
} 