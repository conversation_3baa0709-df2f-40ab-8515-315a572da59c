﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LibraryManagement.API.Migrations
{
    /// <inheritdoc />
    public partial class SimplifyShelfManagement : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>");

            migrationBuilder.DropTable(
                name: "Shelf<PERSON>lot<PERSON>");

            migrationBuilder.AddColumn<int>(
                name: "BookshelfId",
                table: "Books",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LocationCode",
                table: "Books",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Books_BookshelfId",
                table: "Books",
                column: "BookshelfId");

            migrationBuilder.AddForeignKey(
                name: "FK_Books_Bookshelves_BookshelfId",
                table: "Books",
                column: "BookshelfId",
                principalTable: "Bookshelves",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Books_Bookshelves_BookshelfId",
                table: "Books");

            migrationBuilder.DropIndex(
                name: "IX_Books_BookshelfId",
                table: "Books");

            migrationBuilder.DropColumn(
                name: "BookshelfId",
                table: "Books");

            migrationBuilder.DropColumn(
                name: "LocationCode",
                table: "Books");

            migrationBuilder.CreateTable(
                name: "ShelfSlots",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    BookshelfId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Size = table.Column<int>(type: "int", nullable: false),
                    SlotCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ShelfSlots", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ShelfSlots_Bookshelves_BookshelfId",
                        column: x => x.BookshelfId,
                        principalTable: "Bookshelves",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ShelfSlotBooks",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    BookId = table.Column<int>(type: "int", nullable: false),
                    ShelfSlotId = table.Column<int>(type: "int", nullable: false),
                    BorrowQuantity = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Quantity = table.Column<int>(type: "int", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ShelfSlotBooks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ShelfSlotBooks_Books_BookId",
                        column: x => x.BookId,
                        principalTable: "Books",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ShelfSlotBooks_ShelfSlots_ShelfSlotId",
                        column: x => x.ShelfSlotId,
                        principalTable: "ShelfSlots",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ShelfSlotBooks_BookId",
                table: "ShelfSlotBooks",
                column: "BookId");

            migrationBuilder.CreateIndex(
                name: "IX_ShelfSlotBooks_ShelfSlotId_BookId",
                table: "ShelfSlotBooks",
                columns: new[] { "ShelfSlotId", "BookId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ShelfSlots_BookshelfId_SlotCode",
                table: "ShelfSlots",
                columns: new[] { "BookshelfId", "SlotCode" },
                unique: true);
        }
    }
}
