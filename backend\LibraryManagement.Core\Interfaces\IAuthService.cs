using LibraryManagement.Core.Entities;

namespace LibraryManagement.Core.Interfaces;

public interface IAuthService
{
    Task<(bool Success, string Token, User? User, string Message)> LoginAsync(string email, string password);
    Task<(bool Success, string Token, User? User, string Message)> RegisterAsync(string email, string password, string username, string firstName, string lastName);
    Task<(bool Success, string Message)> VerifyEmailAsync(string token);
    Task<(bool Success, string Message)> RequestPasswordResetAsync(string email);
    Task<(bool Success, string Message)> ResetPasswordAsync(string token, string newPassword);
    Task<(bool Success, string Message)> ResendVerificationEmailAsync(string email);
    Task<(bool Success, string Message)> ForceVerifyEmailAsync(int userId, int adminId);
    string GenerateJwtToken(User user);
    string HashPassword(string password);
    bool VerifyPassword(string password, string hashedPassword);
} 