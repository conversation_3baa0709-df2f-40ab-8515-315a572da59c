import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ThemeService, ThemeMode } from '../../services/theme.service';

@Component({
  selector: 'app-theme-toggle',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatTooltipModule
  ],
  template: `
    <button 
      mat-icon-button 
      [matMenuTriggerFor]="themeMenu"
      matTooltip="Chuyển đổi giao diện"
      class="theme-toggle-btn">
      <mat-icon>{{ getThemeIcon() }}</mat-icon>
    </button>

    <mat-menu #themeMenu="matMenu" class="theme-menu">
      <button 
        mat-menu-item 
        (click)="setTheme('light')"
        [class.active]="themeService.themeMode() === 'light'">
        <mat-icon>light_mode</mat-icon>
        <span>Sáng</span>
        <mat-icon class="check-icon" *ngIf="themeService.themeMode() === 'light'">check</mat-icon>
      </button>
      
      <button 
        mat-menu-item 
        (click)="setTheme('dark')"
        [class.active]="themeService.themeMode() === 'dark'">
        <mat-icon>dark_mode</mat-icon>
        <span>Tối</span>
        <mat-icon class="check-icon" *ngIf="themeService.themeMode() === 'dark'">check</mat-icon>
      </button>
      
      <button 
        mat-menu-item 
        (click)="setTheme('auto')"
        [class.active]="themeService.themeMode() === 'auto'">
        <mat-icon>brightness_auto</mat-icon>
        <span>Tự động</span>
        <mat-icon class="check-icon" *ngIf="themeService.themeMode() === 'auto'">check</mat-icon>
      </button>
    </mat-menu>
  `,
  styles: [`
    .theme-toggle-btn {
      transition: all 0.3s ease;
    }

    .theme-toggle-btn:hover {
      transform: scale(1.1);
    }

    .theme-menu {
      .mat-mdc-menu-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        transition: all 0.2s ease;

        &.active {
          background-color: var(--color-primary);
          color: var(--color-on-primary);
          
          mat-icon {
            color: var(--color-on-primary);
          }
        }

        &:hover:not(.active) {
          background-color: var(--color-surface-variant);
        }

        span {
          flex: 1;
        }

        .check-icon {
          margin-left: auto;
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }
    }
  `]
})
export class ThemeToggleComponent {
  constructor(public themeService: ThemeService) {}

  setTheme(mode: ThemeMode): void {
    this.themeService.setThemeMode(mode);
  }

  getThemeIcon(): string {
    const mode = this.themeService.themeMode();
    switch (mode) {
      case 'light':
        return 'light_mode';
      case 'dark':
        return 'dark_mode';
      case 'auto':
        return 'brightness_auto';
      default:
        return 'brightness_auto';
    }
  }
}
