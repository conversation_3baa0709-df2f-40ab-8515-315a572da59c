import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import {
  Category,
  CreateCategory,
  UpdateCategory,
  CategoryStatistics,
  CategoryValidation,
  CategorySearchParams,
  CategorySearchResult
} from '../models/category.model';

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;
  errors?: string[];
}

@Injectable({ providedIn: 'root' })
export class CategoryService {
  private readonly apiUrl = `${environment.apiUrl}/categories`;

  constructor(private http: HttpClient) { }

  // Get all categories
  getCategories(): Observable<Category[]> {
    return this.http.get<Category[]>(this.apiUrl).pipe(
      catchError(this.handleError)
    );
  }

  // Get category by ID
  getCategoryById(id: number): Observable<Category> {
    return this.http.get<Category>(`${this.apiUrl}/${id}`).pipe(
      catchError(this.handleError)
    );
  }

  // Create new category
  createCategory(category: CreateCategory): Observable<ApiResponse<Category>> {
    return this.http.post<ApiResponse<Category>>(this.apiUrl, category).pipe(
      catchError(this.handleError)
    );
  }

  // Update existing category
  updateCategory(id: number, category: UpdateCategory): Observable<ApiResponse<void>> {
    return this.http.put<ApiResponse<void>>(`${this.apiUrl}/${id}`, category).pipe(
      catchError(this.handleError)
    );
  }

  // Delete category
  deleteCategory(id: number): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`).pipe(
      catchError(this.handleError)
    );
  }

  // Get category statistics
  getCategoryStatistics(id: number): Observable<CategoryStatistics> {
    return this.http.get<CategoryStatistics>(`${this.apiUrl}/${id}/statistics`).pipe(
      catchError(this.handleError)
    );
  }

  // Search categories
  searchCategories(params: CategorySearchParams): Observable<CategorySearchResult> {
    let httpParams = new HttpParams();

    if (params.query) httpParams = httpParams.set('query', params.query);
    if (params.page) httpParams = httpParams.set('page', params.page.toString());
    if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());
    if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);
    if (params.sortDirection) httpParams = httpParams.set('sortDirection', params.sortDirection);

    return this.http.get<CategorySearchResult>(`${this.apiUrl}/search`, { params: httpParams }).pipe(
      catchError(this.handleError)
    );
  }

  // Validate category data
  validateCategory(category: CreateCategory): Observable<CategoryValidation> {
    return this.http.post<CategoryValidation>(`${this.apiUrl}/validate`, category).pipe(
      catchError(this.handleError)
    );
  }

  // Private error handler
  private handleError = (error: HttpErrorResponse): Observable<never> => {
    let errorMessage = 'Đã xảy ra lỗi không xác định';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Lỗi: ${error.error.message}`;
    } else {
      // Server-side error
      switch (error.status) {
        case 400:
          errorMessage = error.error?.message || 'Dữ liệu không hợp lệ';
          break;
        case 404:
          errorMessage = 'Không tìm thấy thể loại';
          break;
        case 409:
          errorMessage = 'Thể loại đã tồn tại hoặc có xung đột dữ liệu';
          break;
        case 500:
          errorMessage = 'Lỗi máy chủ nội bộ';
          break;
        default:
          errorMessage = `Lỗi ${error.status}: ${error.error?.message || error.message}`;
      }
    }

    console.error('CategoryService Error:', error);
    return throwError(() => new Error(errorMessage));
  };
}
