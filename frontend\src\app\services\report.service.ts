import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ReportService {
  private apiUrl = `${environment.apiUrl}/reports`;

  constructor(private http: HttpClient) { }

  getDashboardStats(): Observable<DashboardStats> {
    return this.http.get<DashboardStats>(`${this.apiUrl}/dashboard`);
  }

  getPopularBooks(count: number = 5): Observable<PopularBook[]> {
    return this.http.get<PopularBook[]>(`${this.apiUrl}/popular-books?count=${count}`);
  }

  getActiveMembers(count: number = 5): Observable<ActiveMember[]> {
    return this.http.get<ActiveMember[]>(`${this.apiUrl}/active-members?count=${count}`);
  }

  getCategoryStats(): Observable<CategoryStats[]> {
    return this.http.get<CategoryStats[]>(`${this.apiUrl}/category-stats`);
  }

  getMonthlyStats(year: number = 0): Observable<MonthlyStats[]> {
    return this.http.get<MonthlyStats[]>(`${this.apiUrl}/monthly-stats?year=${year}`);
  }

  getDailyStats(startDate?: Date, endDate?: Date): Observable<DailyStats[]> {
    let params = new HttpParams();
    if (startDate) {
      params = params.set('startDate', startDate.toISOString());
    }
    if (endDate) {
      params = params.set('endDate', endDate.toISOString());
    }
    return this.http.get<DailyStats[]>(`${this.apiUrl}/daily-stats`, { params });
  }

  getOverdueBooks(): Observable<BorrowRecord[]> {
    return this.http.get<BorrowRecord[]>(`${this.apiUrl}/overdue-books`);
  }

  getFineCollection(startDate?: Date, endDate?: Date): Observable<FineCollectionReport> {
    let params = new HttpParams();
    if (startDate) {
      params = params.set('startDate', startDate.toISOString());
    }
    if (endDate) {
      params = params.set('endDate', endDate.toISOString());
    }
    return this.http.get<FineCollectionReport>(`${this.apiUrl}/fine-collection`, { params });
  }

  generateCustomReport(request: ReportRequest): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/custom-report`, request);
  }
}

export interface DashboardStats {
  totalBooks: number;
  totalMembers: number;
  totalBorrows: number;
  activeBorrows: number;
  overdueBooks: number;
  totalFines: number;
  totalCategories: number;
  totalUsers: number;
}

export interface PopularBook {
  bookId: number;
  title: string;
  author: string;
  borrowCount: number;
  imageUrl?: string;
  categoryName: string;
}

export interface ActiveMember {
  memberId: number;
  fullName: string;
  email: string;
  borrowCount: number;
  status: MemberStatus;
}

export interface CategoryStats {
  categoryId: number;
  categoryName: string;
  bookCount: number;
  borrowCount: number;
}

export interface MonthlyStats {
  year: number;
  month: number;
  monthName: string;
  newBorrows: number;
  returns: number;
  newMembers: number;
  totalFines: number;
}

export interface DailyStats {
  date: string;
  newBorrows: number;
  returns: number;
  totalFines: number;
}

export interface FineCollectionReport {
  startDate: string;
  endDate: string;
  totalFineCollected: number;
  averageFine: number;
  maxFine: number;
  totalRecords: number;
  fineDetails: FineDetail[];
}

export interface FineDetail {
  borrowId: number;
  bookId: number;
  memberId: number;
  returnDate: string;
  daysOverdue: number;
  fine: number;
}

export interface ReportRequest {
  startDate?: Date;
  endDate?: Date;
  reportType: ReportType;
  categoryId?: number;
  memberId?: number;
  borrowStatus?: BorrowStatus;
}

export enum ReportType {
  Daily = 1,
  Monthly = 2,
  Yearly = 3,
  CategoryWise = 4,
  MemberActivity = 5,
  FineCollection = 6,
  BookStatus = 7
}

export enum BorrowStatus {
  Borrowed = 1,
  Returned = 2,
  Overdue = 3,
  Lost = 4,
  Renewed = 5
}

export enum MemberStatus {
  Active = 1,
  Suspended = 2,
  Expired = 3,
  Banned = 4
}

export interface BorrowRecord {
  id: number;
  bookId: number;
  bookTitle: string;
  bookAuthor: string;
  memberId: number;
  memberName: string;
  borrowDate: string;
  dueDate: string;
  returnDate?: string;
  status: BorrowStatus;
  statusName: string;
  notes?: string;
  fine?: number;
  isOverdue: boolean;
  daysOverdue: number;
  createdAt: string;
}