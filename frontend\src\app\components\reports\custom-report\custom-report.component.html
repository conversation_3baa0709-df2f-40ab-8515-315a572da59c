<div class="custom-report-container">
  <div class="page-title">
    <h1>B<PERSON>o cáo tùy chỉnh</h1>
  </div>

  <mat-card class="report-form-card">
    <mat-card-content>
      <form [formGroup]="reportForm" (ngSubmit)="generateReport()">
        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Loại báo cáo</mat-label>
            <mat-select formControlName="reportType" (selectionChange)="onReportTypeChange()">
              <mat-option *ngFor="let type of reportTypes" [value]="type.value">
                {{ type.label }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="reportForm.get('reportType')?.hasError('required')">
              Vui lòng chọn loại báo cáo
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row date-range">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Từ ngày</mat-label>
            <input matInput [matDatepicker]="startPicker" formControlName="startDate">
            <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
            <mat-datepicker #startPicker></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Đến ngày</mat-label>
            <input matInput [matDatepicker]="endPicker" formControlName="endDate">
            <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
            <mat-datepicker #endPicker></mat-datepicker>
          </mat-form-field>
        </div>

        <div class="form-row" *ngIf="shouldShowField('categoryId')">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Danh mục</mat-label>
            <input matInput formControlName="categoryId" placeholder="Nhập ID danh mục">
            <mat-error *ngIf="reportForm.get('categoryId')?.hasError('required')">
              Vui lòng nhập ID danh mục
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row" *ngIf="shouldShowField('memberId')">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Thành viên</mat-label>
            <input matInput formControlName="memberId" placeholder="Nhập ID thành viên">
            <mat-error *ngIf="reportForm.get('memberId')?.hasError('required')">
              Vui lòng nhập ID thành viên
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row" *ngIf="shouldShowField('borrowStatus')">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Trạng thái mượn</mat-label>
            <mat-select formControlName="borrowStatus">
              <mat-option *ngFor="let status of borrowStatuses" [value]="status.value">
                {{ status.label }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="reportForm.get('borrowStatus')?.hasError('required')">
              Vui lòng chọn trạng thái mượn
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-actions">
          <button mat-raised-button color="primary" type="submit" [disabled]="reportForm.invalid || isLoading">
            <mat-icon>assessment</mat-icon> Tạo báo cáo
          </button>
          <button mat-button type="button" (click)="resetForm()" [disabled]="isLoading">
            <mat-icon>refresh</mat-icon> Đặt lại
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <div *ngIf="isLoading" class="loading-container">
    <mat-progress-bar mode="indeterminate"></mat-progress-bar>
    <p>Đang tạo báo cáo...</p>
  </div>

  <div *ngIf="error" class="error-container">
    <p class="error-message">{{ error }}</p>
  </div>

  <div *ngIf="reportData && reportData.data && reportData.data.length > 0" class="report-results">
    <div class="report-header">
      <h2>{{ getReportTypeName() }}</h2>
      <div class="report-actions">
        <button mat-icon-button color="primary" matTooltip="Xuất CSV" (click)="exportToCsv()">
          <mat-icon>file_download</mat-icon>
        </button>
        <button mat-icon-button color="primary" matTooltip="In báo cáo" (click)="printReport()">
          <mat-icon>print</mat-icon>
        </button>
      </div>
    </div>

    <div class="report-info">
      <p><strong>Khoảng thời gian:</strong> {{ reportForm.get('startDate')?.value ? (reportForm.get('startDate')?.value | date:'dd/MM/yyyy') : '' }} - {{ reportForm.get('endDate')?.value ? (reportForm.get('endDate')?.value | date:'dd/MM/yyyy') : '' }}</p>
      <p *ngIf="reportForm.get('categoryId')?.value"><strong>Danh mục:</strong> {{ reportForm.get('categoryId')?.value }}</p>
      <p *ngIf="reportForm.get('memberId')?.value"><strong>Thành viên:</strong> {{ reportForm.get('memberId')?.value }}</p>
      <p *ngIf="reportForm.get('borrowStatus')?.value"><strong>Trạng thái mượn:</strong> {{ getBorrowStatusName(reportForm.get('borrowStatus')?.value) }}</p>
    </div>

    <div class="report-summary" *ngIf="reportData.summary">
      <p>{{ reportData.summary }}</p>
    </div>

    <div class="report-table-container">
      <table class="report-table">
        <thead>
          <tr>
            <th *ngFor="let header of reportData.data[0] | keyvalue">{{ formatHeader(header.key) }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let row of reportData.data">
            <td *ngFor="let cell of row | keyvalue">
              <!-- Format date values -->
              <ng-container *ngIf="cell.value && isDateField(cell.key); else regularCell">
                {{ formatDateValue(cell.value) }}
              </ng-container>
              <ng-template #regularCell>
                {{ cell.value }}
              </ng-template>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="report-footer">
      <p>Tổng số bản ghi: {{ reportData.data.length }}</p>
    </div>
  </div>

  <div *ngIf="reportData && (!reportData.data || reportData.data.length === 0)" class="no-data">
    <p>Không có dữ liệu cho báo cáo này với các tiêu chí đã chọn.</p>
  </div>
</div>