import { Injectable, signal, effect } from '@angular/core';

export type ThemeMode = 'light' | 'dark' | 'auto';

export interface ThemeColors {
  primary: string;
  primaryVariant: string;
  secondary: string;
  secondaryVariant: string;
  background: string;
  surface: string;
  surfaceVariant: string;
  error: string;
  warning: string;
  success: string;
  info: string;
  onPrimary: string;
  onSecondary: string;
  onBackground: string;
  onSurface: string;
  onError: string;
  outline: string;
  shadow: string;
}

export interface ThemeConfig {
  mode: ThemeMode;
  colors: ThemeColors;
  borderRadius: {
    small: string;
    medium: string;
    large: string;
    extraLarge: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
  };
  typography: {
    fontFamily: string;
    fontSize: {
      xs: string;
      sm: string;
      base: string;
      lg: string;
      xl: string;
      '2xl': string;
      '3xl': string;
    };
    fontWeight: {
      light: number;
      normal: number;
      medium: number;
      semibold: number;
      bold: number;
    };
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_KEY = 'library-theme-mode';
  
  // Reactive theme mode signal
  public themeMode = signal<ThemeMode>('light');
  
  // Light theme colors
  private lightColors: ThemeColors = {
    primary: '#1976d2',
    primaryVariant: '#1565c0',
    secondary: '#dc004e',
    secondaryVariant: '#c51162',
    background: '#fafafa',
    surface: '#ffffff',
    surfaceVariant: '#f5f5f5',
    error: '#d32f2f',
    warning: '#f57c00',
    success: '#388e3c',
    info: '#1976d2',
    onPrimary: '#ffffff',
    onSecondary: '#ffffff',
    onBackground: '#212121',
    onSurface: '#212121',
    onError: '#ffffff',
    outline: '#e0e0e0',
    shadow: 'rgba(0, 0, 0, 0.1)'
  };

  // Dark theme colors
  private darkColors: ThemeColors = {
    primary: '#90caf9',
    primaryVariant: '#64b5f6',
    secondary: '#f48fb1',
    secondaryVariant: '#f06292',
    background: '#121212',
    surface: '#1e1e1e',
    surfaceVariant: '#2d2d2d',
    error: '#cf6679',
    warning: '#ffb74d',
    success: '#81c784',
    info: '#64b5f6',
    onPrimary: '#000000',
    onSecondary: '#000000',
    onBackground: '#ffffff',
    onSurface: '#ffffff',
    onError: '#000000',
    outline: '#424242',
    shadow: 'rgba(0, 0, 0, 0.3)'
  };

  // Base theme configuration
  private baseTheme: Omit<ThemeConfig, 'mode' | 'colors'> = {
    borderRadius: {
      small: '8px',
      medium: '12px',
      large: '16px',
      extraLarge: '24px'
    },
    spacing: {
      xs: '4px',
      sm: '8px',
      md: '16px',
      lg: '24px',
      xl: '32px',
      xxl: '48px'
    },
    typography: {
      fontFamily: '"Roboto", "Helvetica Neue", Arial, sans-serif',
      fontSize: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '2rem'
      },
      fontWeight: {
        light: 300,
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      }
    },
    shadows: {
      sm: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
      md: '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
      lg: '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)',
      xl: '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)'
    }
  };

  constructor() {
    // Load saved theme mode
    const savedMode = localStorage.getItem(this.THEME_KEY) as ThemeMode;
    if (savedMode && ['light', 'dark', 'auto'].includes(savedMode)) {
      this.themeMode.set(savedMode);
    } else {
      // Detect system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      this.themeMode.set(prefersDark ? 'dark' : 'light');
    }

    // Apply theme when mode changes
    effect(() => {
      this.applyTheme();
    });

    // Listen for system theme changes when in auto mode
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (this.themeMode() === 'auto') {
        this.applyTheme();
      }
    });
  }

  setThemeMode(mode: ThemeMode): void {
    this.themeMode.set(mode);
    localStorage.setItem(this.THEME_KEY, mode);
  }

  getCurrentTheme(): ThemeConfig {
    const isDark = this.isDarkMode();
    return {
      mode: this.themeMode(),
      colors: isDark ? this.darkColors : this.lightColors,
      ...this.baseTheme
    };
  }

  isDarkMode(): boolean {
    const mode = this.themeMode();
    if (mode === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return mode === 'dark';
  }

  private applyTheme(): void {
    const theme = this.getCurrentTheme();
    const root = document.documentElement;

    // Apply CSS custom properties
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${this.kebabCase(key)}`, value);
    });

    Object.entries(theme.borderRadius).forEach(([key, value]) => {
      root.style.setProperty(`--radius-${key}`, value);
    });

    Object.entries(theme.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--spacing-${key}`, value);
    });

    Object.entries(theme.typography.fontSize).forEach(([key, value]) => {
      root.style.setProperty(`--font-size-${key}`, value);
    });

    Object.entries(theme.typography.fontWeight).forEach(([key, value]) => {
      root.style.setProperty(`--font-weight-${key}`, value.toString());
    });

    Object.entries(theme.shadows).forEach(([key, value]) => {
      root.style.setProperty(`--shadow-${key}`, value);
    });

    // Set font family
    root.style.setProperty('--font-family', theme.typography.fontFamily);

    // Add theme class to body
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${this.isDarkMode() ? 'dark' : 'light'}`);
  }

  private kebabCase(str: string): string {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
  }

  // Utility methods for components
  getColor(colorName: keyof ThemeColors): string {
    return this.getCurrentTheme().colors[colorName];
  }

  getSpacing(size: keyof ThemeConfig['spacing']): string {
    return this.getCurrentTheme().spacing[size];
  }

  getBorderRadius(size: keyof ThemeConfig['borderRadius']): string {
    return this.getCurrentTheme().borderRadius[size];
  }

  getShadow(size: keyof ThemeConfig['shadows']): string {
    return this.getCurrentTheme().shadows[size];
  }
}
