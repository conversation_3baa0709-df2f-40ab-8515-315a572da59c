-- Script to update Admin password with correct BCrypt hash
-- Password: Admin123!

USE LibraryManagementDb;
GO

-- Update admin password hash (BCrypt hash for "Admin123!")
UPDATE Users 
SET PasswordHash = '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'
WHERE Email = '<EMAIL>';

-- Also ensure admin is active and email verified
UPDATE Users 
SET IsActive = 1, EmailVerified = 1, EmailVerificationToken = NULL, EmailVerificationTokenExpiry = NULL
WHERE Email = '<EMAIL>';

-- Display admin account info
SELECT 
    Id,
    Username,
    Email,
    FirstName,
    LastName,
    Role,
    IsActive,
    EmailVerified,
    CreatedAt
FROM Users 
WHERE Email = '<EMAIL>';

PRINT 'Admin password updated successfully!';
PRINT 'Login credentials:';
PRINT 'Email: <EMAIL>';
PRINT 'Password: Admin123!';
PRINT 'Username: admin'; 