import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
// import { MatChipsModule } from '@angular/material/chips'; // Not available in Angular 18
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatDividerModule } from '@angular/material/divider';

import { ShelfService } from '../../services/shelf.service';
import { Shelf, ShelfStatus, BookInShelf } from '../../models/shelf.model';

@Component({
  selector: 'app-shelf-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    // MatChipsModule, // Not available
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTabsModule,
    MatProgressBarModule,
    MatDividerModule
  ],
  template: `
    <div class="shelf-detail-container" *ngIf="!isLoading">
      <!-- Header Card -->
      <mat-card class="shelf-header-card">
        <mat-card-header>
          <div mat-card-avatar class="shelf-avatar">
            <mat-icon>inventory</mat-icon>
          </div>
          <mat-card-title>{{ shelf?.name }}</mat-card-title>
          <mat-card-subtitle>
            <mat-icon>location_on</mat-icon>
            {{ shelf?.zoneName }}
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <!-- Status and Basic Info -->
          <div class="shelf-info-grid">
            <div class="info-item">
              <mat-icon>info</mat-icon>
              <span class="label">Trạng thái:</span>
              <span class="status-chip" [class]="getStatusClass(shelf?.status)">
                <mat-icon>{{ getStatusIcon(shelf?.status) }}</mat-icon>
                {{ getStatusText(shelf?.status) }}
              </span>
            </div>

            <div class="info-item">
              <mat-icon>inventory_2</mat-icon>
              <span class="label">Sức chứa:</span>
              <span class="value">{{ shelf?.capacity }} cuốn</span>
            </div>

            <div class="info-item">
              <mat-icon>book</mat-icon>
              <span class="label">Hiện tại:</span>
              <span class="value">{{ shelf?.currentCount }} cuốn</span>
            </div>

            <div class="info-item">
              <mat-icon>space_bar</mat-icon>
              <span class="label">Còn trống:</span>
              <span class="value">{{ shelf?.availableSpace }} cuốn</span>
            </div>
          </div>

          <!-- Capacity Progress Bar -->
          <div class="capacity-section">
            <div class="capacity-header">
              <span>Tỷ lệ sử dụng</span>
              <span>{{ getUsagePercentage() }}%</span>
            </div>
            <mat-progress-bar 
              mode="determinate" 
              [value]="getUsagePercentage()"
              [color]="getProgressBarColor()">
            </mat-progress-bar>
          </div>

          <!-- Description -->
          <div class="description-section" *ngIf="shelf?.description">
            <mat-divider></mat-divider>
            <h4>
              <mat-icon>description</mat-icon>
              Mô tả
            </h4>
            <p>{{ shelf?.description }}</p>
          </div>
        </mat-card-content>

        <mat-card-actions align="end">
          <button mat-button (click)="goBack()">
            <mat-icon>arrow_back</mat-icon>
            Quay lại
          </button>
          
          <button mat-button [routerLink]="['/shelves', shelf?.id, 'books']">
            <mat-icon>book</mat-icon>
            Quản lý Sách
          </button>
          
          <button mat-raised-button color="primary" [routerLink]="['/shelves/edit', shelf?.id]">
            <mat-icon>edit</mat-icon>
            Chỉnh sửa
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Books Preview Card -->
      <mat-card class="books-preview-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>book</mat-icon>
            Sách trong kệ ({{ booksInShelf.length }})
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <div *ngIf="booksInShelf.length === 0" class="empty-state">
            <mat-icon class="empty-icon">book_online</mat-icon>
            <h3>Chưa có sách nào</h3>
            <p>Kệ này chưa có sách nào được xếp vào</p>
            <button mat-raised-button color="primary" [routerLink]="['/shelves', shelf?.id, 'books']">
              <mat-icon>add</mat-icon>
              Thêm sách vào kệ
            </button>
          </div>

          <div *ngIf="booksInShelf.length > 0" class="books-grid">
            <div *ngFor="let book of booksInShelf.slice(0, 6)" class="book-item">
              <div class="book-info">
                <h4>{{ book.title }}</h4>
                <p class="author">{{ book.author }}</p>
                <p class="category">{{ book.categoryName }}</p>
                <div class="location-info">
                  <mat-icon>place</mat-icon>
                  <span>{{ book.locationCode || 'Chưa xác định' }}</span>
                </div>
              </div>
              <div class="book-quantity">
                <span class="quantity-chip">{{ book.stockQuantity }} cuốn</span>
              </div>
            </div>
          </div>

          <div *ngIf="booksInShelf.length > 6" class="show-more">
            <button mat-button [routerLink]="['/shelves', shelf?.id, 'books']">
              <mat-icon>more_horiz</mat-icon>
              Xem thêm {{ booksInShelf.length - 6 }} sách khác
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Statistics Card -->
      <mat-card class="statistics-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>analytics</mat-icon>
            Thống kê
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <div class="stats-grid">
            <div class="stat-item">
              <mat-icon class="stat-icon">category</mat-icon>
              <div class="stat-content">
                <span class="stat-value">{{ getUniqueCategories() }}</span>
                <span class="stat-label">Thể loại</span>
              </div>
            </div>

            <div class="stat-item">
              <mat-icon class="stat-icon">auto_stories</mat-icon>
              <div class="stat-content">
                <span class="stat-value">{{ getTotalBooks() }}</span>
                <span class="stat-label">Tổng sách</span>
              </div>
            </div>

            <div class="stat-item">
              <mat-icon class="stat-icon">trending_up</mat-icon>
              <div class="stat-content">
                <span class="stat-value">{{ getUsagePercentage() }}%</span>
                <span class="stat-label">Sử dụng</span>
              </div>
            </div>

            <div class="stat-item">
              <mat-icon class="stat-icon">schedule</mat-icon>
              <div class="stat-content">
                <span class="stat-value">{{ getFormattedDate(shelf?.createdAt) }}</span>
                <span class="stat-label">Tạo lúc</span>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="loading-container">
      <mat-spinner diameter="50"></mat-spinner>
      <p>Đang tải thông tin kệ sách...</p>
    </div>
  `,
  styles: [`
    .shelf-detail-container {
      max-width: 1200px;
      margin: 20px auto;
      padding: 0 16px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .shelf-header-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .shelf-header-card mat-card-title,
    .shelf-header-card mat-card-subtitle {
      color: white;
    }

    .shelf-avatar {
      background-color: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .shelf-info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 20px;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .info-item .label {
      font-weight: 500;
    }

    .info-item .value {
      font-weight: 600;
    }

    .capacity-section {
      margin: 20px 0;
    }

    .capacity-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .description-section {
      margin-top: 20px;
    }

    .description-section h4 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 16px 0 8px 0;
    }

    .books-preview-card,
    .statistics-card {
      background: white;
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: rgba(0, 0, 0, 0.6);
    }

    .empty-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .books-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;
    }

    .book-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 12px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background: #fafafa;
    }

    .book-info h4 {
      margin: 0 0 4px 0;
      font-size: 14px;
      font-weight: 600;
    }

    .book-info p {
      margin: 2px 0;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.6);
    }

    .location-info {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-top: 4px;
    }

    .location-info mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .show-more {
      text-align: center;
      margin-top: 16px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 20px;
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    .stat-icon {
      color: #666eea;
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .stat-content {
      display: flex;
      flex-direction: column;
    }

    .stat-value {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .stat-label {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.6);
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 400px;
      gap: 20px;
    }

    /* Custom chip styles */
    .status-chip {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
    }

    .quantity-chip {
      display: inline-block;
      padding: 4px 8px;
      background-color: #e3f2fd;
      color: #1976d2;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }

    /* Status chip colors */
    .status-active { background-color: #4caf50; color: white; }
    .status-inactive { background-color: #f44336; color: white; }
    .status-maintenance { background-color: #ff9800; color: white; }
    .status-full { background-color: #9c27b0; color: white; }

    @media (max-width: 768px) {
      .shelf-info-grid {
        grid-template-columns: 1fr;
      }
      
      .books-grid {
        grid-template-columns: 1fr;
      }
      
      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  `]
})
export class ShelfDetailComponent implements OnInit {
  shelf?: Shelf;
  booksInShelf: BookInShelf[] = [];
  isLoading = true;
  shelfId!: number;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private shelfService: ShelfService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.shelfId = parseInt(this.route.snapshot.paramMap.get('id') || '0');
    if (this.shelfId) {
      this.loadShelfData();
      this.loadBooksInShelf();
    } else {
      this.router.navigate(['/shelves']);
    }
  }

  private loadShelfData(): void {
    this.shelfService.getShelfById(this.shelfId).subscribe({
      next: (shelf) => {
        this.shelf = shelf;
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi tải thông tin kệ: ' + error.message, 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
        this.router.navigate(['/shelves']);
      }
    });
  }

  private loadBooksInShelf(): void {
    this.shelfService.getBooksInShelf(this.shelfId).subscribe({
      next: (books) => {
        this.booksInShelf = books;
      },
      error: (error) => {
        console.error('Error loading books in shelf:', error);
      }
    });
  }

  getStatusClass(status?: ShelfStatus): string {
    switch (status) {
      case ShelfStatus.ACTIVE: return 'status-active';
      case ShelfStatus.INACTIVE: return 'status-inactive';
      case ShelfStatus.MAINTENANCE: return 'status-maintenance';
      case ShelfStatus.FULL: return 'status-full';
      default: return 'status-active';
    }
  }

  getStatusIcon(status?: ShelfStatus): string {
    switch (status) {
      case ShelfStatus.ACTIVE: return 'check_circle';
      case ShelfStatus.INACTIVE: return 'cancel';
      case ShelfStatus.MAINTENANCE: return 'build';
      case ShelfStatus.FULL: return 'inventory_2';
      default: return 'check_circle';
    }
  }

  getStatusText(status?: ShelfStatus): string {
    switch (status) {
      case ShelfStatus.ACTIVE: return 'Hoạt động';
      case ShelfStatus.INACTIVE: return 'Không hoạt động';
      case ShelfStatus.MAINTENANCE: return 'Bảo trì';
      case ShelfStatus.FULL: return 'Đầy';
      default: return 'Hoạt động';
    }
  }

  getUsagePercentage(): number {
    if (!this.shelf || this.shelf.capacity === 0) return 0;
    return Math.round((this.shelf.currentCount / this.shelf.capacity) * 100);
  }

  getProgressBarColor(): string {
    const percentage = this.getUsagePercentage();
    if (percentage >= 90) return 'warn';
    if (percentage >= 70) return 'accent';
    return 'primary';
  }

  getUniqueCategories(): number {
    const categories = new Set(this.booksInShelf.map(book => book.categoryName));
    return categories.size;
  }

  getTotalBooks(): number {
    return this.booksInShelf.reduce((total, book) => total + book.stockQuantity, 0);
  }

  getFormattedDate(date?: Date): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('vi-VN');
  }

  goBack(): void {
    this.router.navigate(['/shelves']);
  }
}
