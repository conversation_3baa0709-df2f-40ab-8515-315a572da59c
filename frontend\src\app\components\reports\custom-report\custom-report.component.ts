import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormGroup, FormBuilder, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';

import { ReportService, ReportType, BorrowStatus } from '../../../services/report.service';

@Component({
  selector: 'app-custom-report',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressBarModule,
    MatTooltipModule
  ],
  templateUrl: './custom-report.component.html',
  styleUrls: ['./custom-report.component.scss']
})
export class CustomReportComponent implements OnInit {
  reportForm: FormGroup;
  isLoading = false;
  error = '';
  reportData: any = null;
  reportTypes = [
    { value: ReportType.Daily, label: 'Báo cáo hàng ngày' },
    { value: ReportType.Monthly, label: 'Báo cáo hàng tháng' },
    { value: ReportType.Yearly, label: 'Báo cáo hàng năm' },
    { value: ReportType.CategoryWise, label: 'Báo cáo theo danh mục' },
    { value: ReportType.MemberActivity, label: 'Báo cáo hoạt động thành viên' },
    { value: ReportType.FineCollection, label: 'Báo cáo thu phí phạt' },
    { value: ReportType.BookStatus, label: 'Báo cáo trạng thái sách' }
  ];
  borrowStatuses = [
    { value: BorrowStatus.Borrowed, label: 'Đang mượn' },
    { value: BorrowStatus.Returned, label: 'Đã trả' },
    { value: BorrowStatus.Overdue, label: 'Quá hạn' },
    { value: BorrowStatus.Lost, label: 'Mất sách' },
    { value: BorrowStatus.Renewed, label: 'Đã gia hạn' }
  ];

  constructor(
    private fb: FormBuilder,
    private reportService: ReportService,
    private snackBar: MatSnackBar
  ) {
    this.reportForm = this.fb.group({
      reportType: [ReportType.Monthly, Validators.required],
      startDate: [new Date(new Date().getFullYear(), new Date().getMonth(), 1)],
      endDate: [new Date()],
      categoryId: [''],
      memberId: [''],
      borrowStatus: ['']
    });
  }

  ngOnInit(): void {
    this.onReportTypeChange();
  }

  onReportTypeChange(): void {
    const reportType = this.reportForm.get('reportType')?.value;
    
    // Reset form fields
    this.reportForm.patchValue({
      categoryId: '',
      memberId: '',
      borrowStatus: ''
    });

    // Enable/disable fields based on report type
    if (reportType === ReportType.CategoryWise) {
      this.reportForm.get('categoryId')?.setValidators([Validators.required]);
    } else {
      this.reportForm.get('categoryId')?.clearValidators();
    }

    if (reportType === ReportType.MemberActivity) {
      this.reportForm.get('memberId')?.setValidators([Validators.required]);
    } else {
      this.reportForm.get('memberId')?.clearValidators();
    }

    if (reportType === ReportType.BookStatus) {
      this.reportForm.get('borrowStatus')?.setValidators([Validators.required]);
    } else {
      this.reportForm.get('borrowStatus')?.clearValidators();
    }

    // Update validators
    this.reportForm.get('categoryId')?.updateValueAndValidity();
    this.reportForm.get('memberId')?.updateValueAndValidity();
    this.reportForm.get('borrowStatus')?.updateValueAndValidity();
  }

  generateReport(): void {
    if (this.reportForm.invalid) {
      this.snackBar.open('Vui lòng điền đầy đủ thông tin cần thiết', 'Đóng', { duration: 3000 });
      return;
    }

    this.isLoading = true;
    this.error = '';
    this.reportData = null;

    const reportRequest = {
      reportType: this.reportForm.get('reportType')?.value,
      startDate: this.reportForm.get('startDate')?.value,
      endDate: this.reportForm.get('endDate')?.value,
      categoryId: this.reportForm.get('categoryId')?.value || undefined,
      memberId: this.reportForm.get('memberId')?.value || undefined,
      borrowStatus: this.reportForm.get('borrowStatus')?.value || undefined
    };

    this.reportService.generateCustomReport(reportRequest).subscribe({
      next: (data) => {
        this.reportData = data;
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Không thể tạo báo cáo. Vui lòng thử lại sau.';
        this.isLoading = false;
        this.snackBar.open(this.error, 'Đóng', { duration: 3000 });
      }
    });
  }

  resetForm(): void {
    this.reportForm.reset({
      reportType: ReportType.Monthly,
      startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
      endDate: new Date(),
      categoryId: '',
      memberId: '',
      borrowStatus: ''
    });
    this.onReportTypeChange();
    this.reportData = null;
  }

  exportToCsv(): void {
    if (!this.reportData || !this.reportData.data || this.reportData.data.length === 0) {
      this.snackBar.open('Không có dữ liệu để xuất', 'Đóng', { duration: 3000 });
      return;
    }

    // Get headers from the first data item
    const headers = Object.keys(this.reportData.data[0]);

    // Convert data to CSV format
    const csvData = this.reportData.data.map((item: any) => {
      return headers.map(header => {
        let value = item[header];
        // Format dates if needed
        if (value && typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}T/)) {
          value = new Date(value).toLocaleDateString('vi-VN');
        }
        // Handle numbers
        if (typeof value === 'number') {
          return value.toString();
        }
        // Escape strings with commas
        if (typeof value === 'string' && value.includes(',')) {
          return `"${value}"`;
        }
        return value || '';
      }).join(',');
    });

    // Combine headers and data
    const csvContent = [
      headers.join(','),
      ...csvData
    ].join('\n');

    // Create a Blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `bao-cao-${this.getReportTypeName().toLowerCase().replace(/ /g, '-')}-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  printReport(): void {
    if (!this.reportData || !this.reportData.data || this.reportData.data.length === 0) {
      this.snackBar.open('Không có dữ liệu để in', 'Đóng', { duration: 3000 });
      return;
    }

    const headers = Object.keys(this.reportData.data[0]);
    const printWindow = window.open('', '_blank');
    
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>${this.getReportTypeName()}</title>
            <style>
              body { font-family: Arial, sans-serif; padding: 20px; }
              h1, h2, p { text-align: center; }
              .report-info { margin: 20px 0; padding: 10px; background-color: #f5f5f5; border-radius: 4px; }
              table { width: 100%; border-collapse: collapse; margin-top: 20px; }
              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              th { background-color: #f2f2f2; }
              .summary { margin-top: 20px; font-weight: bold; }
              @media print {
                body { margin: 0; padding: 20px; }
                button { display: none; }
              }
            </style>
          </head>
          <body>
            <h1>${this.getReportTypeName()}</h1>
            <p>Ngày xuất báo cáo: ${new Date().toLocaleDateString('vi-VN')}</p>
            
            <div class="report-info">
              <p><strong>Khoảng thời gian:</strong> ${this.reportForm.get('startDate')?.value ? new Date(this.reportForm.get('startDate')?.value).toLocaleDateString('vi-VN') : 'Tất cả'} - ${this.reportForm.get('endDate')?.value ? new Date(this.reportForm.get('endDate')?.value).toLocaleDateString('vi-VN') : 'Tất cả'}</p>
              ${this.reportForm.get('categoryId')?.value ? `<p><strong>Danh mục:</strong> ${this.reportForm.get('categoryId')?.value}</p>` : ''}
              ${this.reportForm.get('memberId')?.value ? `<p><strong>Thành viên:</strong> ${this.reportForm.get('memberId')?.value}</p>` : ''}
              ${this.reportForm.get('borrowStatus')?.value ? `<p><strong>Trạng thái mượn:</strong> ${this.getBorrowStatusName(this.reportForm.get('borrowStatus')?.value)}</p>` : ''}
            </div>
            
            <table>
              <thead>
                <tr>
                  ${headers.map(header => `<th>${this.formatHeader(header)}</th>`).join('')}
                </tr>
              </thead>
              <tbody>
                ${this.reportData.data.map((item: any) => `
                  <tr>
                    ${headers.map(header => {
                      let value = item[header];
                      // Format dates
                      if (value && typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}T/)) {
                        value = new Date(value).toLocaleDateString('vi-VN');
                      }
                      return `<td>${value !== null && value !== undefined ? value : ''}</td>`;
                    }).join('')}
                  </tr>
                `).join('')}
              </tbody>
            </table>
            
            <div class="summary">
              <p>Tổng số bản ghi: ${this.reportData.data.length}</p>
              ${this.reportData.summary ? `<p>${this.reportData.summary}</p>` : ''}
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
              <button onclick="window.print()">In báo cáo</button>
              <button onclick="window.close()">Đóng</button>
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
    } else {
      this.snackBar.open('Không thể mở cửa sổ in. Vui lòng kiểm tra cài đặt trình duyệt của bạn.', 'Đóng', { duration: 5000 });
    }
  }

  getReportTypeName(): string {
    const reportType = this.reportForm.get('reportType')?.value;
    const reportTypeObj = this.reportTypes.find(type => type.value === reportType);
    return reportTypeObj ? reportTypeObj.label : 'Báo cáo tùy chỉnh';
  }

  getBorrowStatusName(statusValue: number): string {
    const status = this.borrowStatuses.find(s => s.value === statusValue);
    return status ? status.label : 'Không xác định';
  }

  formatHeader(key: any): string {
    // Convert camelCase to Title Case with spaces
    return String(key)
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase());
  }

  shouldShowField(field: string): boolean {
    const reportType = this.reportForm.get('reportType')?.value;
    
    switch (field) {
      case 'categoryId':
        return reportType === ReportType.CategoryWise;
      case 'memberId':
        return reportType === ReportType.MemberActivity;
      case 'borrowStatus':
        return reportType === ReportType.BookStatus;
      default:
        return false;
    }
  }

  // Helper method to check if a field is a date field
  isDateField(key: any): boolean {
    if (!key) return false;
    const keyStr = String(key).toLowerCase();
    return keyStr.includes('date') || keyStr.includes('ngày');
  }

  // Helper method to safely format date values
  formatDateValue(value: any): string {
    if (!value) return '';
    
    try {
      // Try to parse as date if it's a string that looks like a date
      if (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}T/)) {
        return new Date(value).toLocaleDateString('vi-VN');
      }
      // If it's already a Date object
      if (value instanceof Date) {
        return value.toLocaleDateString('vi-VN');
      }
      // For other values, return as is
      return String(value);
    } catch (error) {
      return String(value || '');
    }
  }
}