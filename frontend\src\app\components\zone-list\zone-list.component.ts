import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { HttpClientModule } from '@angular/common/http';
import { ZoneService } from 'src/app/services/zone.service';
import { Zone } from 'src/app/services/zone.service';

@Component({
  selector: 'app-zone-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatTableModule,
    MatIconModule,
    HttpClientModule
  ],
  template: `
    <mat-card>
      <mat-card-title>Quản lý K<PERSON> vực</mat-card-title>

      <form (ngSubmit)="submitZone()" style="display: flex; gap: 12px; flex-wrap: wrap; margin-bottom: 16px;">
        <mat-form-field appearance="outline" style="min-width: 220px;">
          <mat-label>Tên khu vực</mat-label>
          <input matInput [(ngModel)]="form.name" name="name" required />
        </mat-form-field>

        <mat-form-field appearance="outline" style="min-width: 320px;">
          <mat-label>Mô tả</mat-label>
          <input matInput [(ngModel)]="form.description" name="description" />
        </mat-form-field>

        <div style="align-self: flex-end;">
          <button mat-raised-button color="primary" type="submit">
            {{ isEditMode ? 'Cập nhật' : 'Thêm khu vực' }}
          </button>
          <button mat-stroked-button type="button" (click)="cancelEdit()" *ngIf="isEditMode">
            Hủy
          </button>
        </div>
      </form>

      <table mat-table [dataSource]="zones" class="mat-elevation-z1" style="width: 100%;">
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Tên khu vực</th>
          <td mat-cell *matCellDef="let z">{{ z.name }}</td>
        </ng-container>

        <ng-container matColumnDef="description">
          <th mat-header-cell *matHeaderCellDef>Mô tả</th>
          <td mat-cell *matCellDef="let z">{{ z.description }}</td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Thao tác</th>
          <td mat-cell *matCellDef="let z">
            <button mat-icon-button (click)="editZone(z)">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button color="warn" (click)="deleteZone(z)">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </mat-card>
  `
})
export class ZoneListComponent implements OnInit {
  zones: Zone[] = [];
  isEditMode = false;
  editingZoneId: number | null = null;

  form: Partial<Zone> = {
    name: '',
    description: ''
  };

  displayedColumns = ['name', 'description', 'actions'];

  constructor(private zoneService: ZoneService) {}

  ngOnInit(): void {
    this.loadZones();
  }

  loadZones(): void {
    this.zoneService.getAllZones().subscribe({
      next: (data) => (this.zones = data),
      error: (err) => console.error('Lỗi tải khu vực:', err)
    });
  }

  submitZone() {
    if (!this.form.name?.trim()) {
      alert('Tên khu vực là bắt buộc');
      return;
    }

    if (this.isEditMode && this.editingZoneId !== null) {
      this.zoneService.updateZone(this.editingZoneId, {
        id: this.editingZoneId,
        ...this.form
      } as Zone).subscribe({
        next: () => {
          this.loadZones();
          this.cancelEdit();
        },
        error: (err) => console.error('Lỗi cập nhật khu vực:', err)
      });
    } else {
      this.zoneService.createZone({
        id: 0,
        ...this.form
      } as Zone).subscribe({
        next: () => {
          this.loadZones();
          this.resetForm();
        },
        error: (err) => console.error('Lỗi thêm khu vực:', err)
      });
    }
  }

  editZone(zone: Zone) {
    this.form = { name: zone.name, description: zone.description };
    this.editingZoneId = zone.id;
    this.isEditMode = true;
  }

  deleteZone(zone: Zone) {
    const confirmed = confirm(`Bạn chắc chắn muốn xóa khu vực "${zone.name}"?`);
    if (confirmed) {
      this.zoneService.deleteZone(zone.id).subscribe({
        next: () => this.loadZones(),
        error: (err) => console.error('Lỗi xóa khu vực:', err)
      });
    }
  }

  cancelEdit() {
    this.resetForm();
    this.isEditMode = false;
    this.editingZoneId = null;
  }

  resetForm() {
    this.form = { name: '', description: '' };
  }
}
