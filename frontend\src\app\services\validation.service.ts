import { Injectable } from '@angular/core';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { Observable, of, timer } from 'rxjs';
import { map, switchMap, catchError } from 'rxjs/operators';
import { ShelfService } from './shelf.service';
import { ZoneService } from './zone.service';

@Injectable({
  providedIn: 'root'
})
export class ValidationService {

  constructor(
    private shelfService: ShelfService,
    private zoneService: ZoneService
  ) {}

  // Custom validators
  static requiredTrim(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (!value || typeof value !== 'string' || value.trim().length === 0) {
      return { requiredTrim: true };
    }
    return null;
  }

  static positiveNumber(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (value !== null && (isNaN(value) || value <= 0)) {
      return { positiveNumber: true };
    }
    return null;
  }

  static maxCapacity(max: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      if (value !== null && value > max) {
        return { maxCapacity: { max, actual: value } };
      }
      return null;
    };
  }

  // Async validators
  uniqueShelfNameInZone(zoneId: number, excludeShelfId?: number): ValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      if (!control.value || !zoneId) {
        return of(null);
      }

      return timer(500).pipe(
        switchMap(() => this.shelfService.searchShelves(control.value)),
        map(shelves => {
          const duplicate = shelves.find(shelf => 
            shelf.name.toLowerCase() === control.value.toLowerCase() &&
            shelf.zoneId === zoneId &&
            shelf.id !== excludeShelfId
          );
          
          return duplicate ? { uniqueShelfNameInZone: true } : null;
        }),
        catchError(() => of(null))
      );
    };
  }

  zoneExists(): ValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      if (!control.value) {
        return of(null);
      }

      return this.zoneService.getAllZones().pipe(
        map(zones => {
          const exists = zones.some(zone => zone.id === control.value);
          return exists ? null : { zoneExists: false };
        }),
        catchError(() => of({ zoneExists: false }))
      );
    };
  }

  // Validation messages
  getErrorMessage(fieldName: string, errors: ValidationErrors): string {
    if (errors['required'] || errors['requiredTrim']) {
      return `${fieldName} là bắt buộc`;
    }

    if (errors['minlength']) {
      const requiredLength = errors['minlength'].requiredLength;
      return `${fieldName} phải có ít nhất ${requiredLength} ký tự`;
    }

    if (errors['maxlength']) {
      const requiredLength = errors['maxlength'].requiredLength;
      return `${fieldName} không được quá ${requiredLength} ký tự`;
    }

    if (errors['min']) {
      const min = errors['min'].min;
      return `${fieldName} phải lớn hơn hoặc bằng ${min}`;
    }

    if (errors['max']) {
      const max = errors['max'].max;
      return `${fieldName} không được lớn hơn ${max}`;
    }

    if (errors['positiveNumber']) {
      return `${fieldName} phải là số dương`;
    }

    if (errors['maxCapacity']) {
      const max = errors['maxCapacity'].max;
      return `${fieldName} không được vượt quá ${max}`;
    }

    if (errors['uniqueShelfNameInZone']) {
      return `Tên kệ đã tồn tại trong khu vực này`;
    }

    if (errors['zoneExists']) {
      return `Khu vực không tồn tại`;
    }

    if (errors['email']) {
      return `${fieldName} không đúng định dạng email`;
    }

    if (errors['pattern']) {
      return `${fieldName} không đúng định dạng`;
    }

    // Default error message
    return `${fieldName} không hợp lệ`;
  }

  // Form validation helpers
  validateShelfForm(formData: any): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Name validation
    if (!formData.name || formData.name.trim().length === 0) {
      errors.push('Tên kệ là bắt buộc');
    } else if (formData.name.length < 2) {
      errors.push('Tên kệ phải có ít nhất 2 ký tự');
    } else if (formData.name.length > 100) {
      errors.push('Tên kệ không được quá 100 ký tự');
    }

    // Zone validation
    if (!formData.zoneId || formData.zoneId <= 0) {
      errors.push('Vui lòng chọn khu vực');
    }

    // Capacity validation
    if (!formData.capacity || formData.capacity <= 0) {
      errors.push('Sức chứa phải lớn hơn 0');
    } else if (formData.capacity > 1000) {
      errors.push('Sức chứa không được quá 1000');
    } else if (formData.capacity > 500) {
      warnings.push('Sức chứa lớn có thể khó quản lý');
    }

    // Description validation
    if (formData.description && formData.description.length > 500) {
      errors.push('Mô tả không được quá 500 ký tự');
    }

    // Status validation
    const validStatuses = ['Active', 'Inactive', 'Maintenance', 'Full'];
    if (formData.status && !validStatuses.includes(formData.status)) {
      errors.push('Trạng thái không hợp lệ');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Real-time validation for specific fields
  validateShelfName(name: string): { isValid: boolean; message?: string } {
    if (!name || name.trim().length === 0) {
      return { isValid: false, message: 'Tên kệ là bắt buộc' };
    }

    if (name.length < 2) {
      return { isValid: false, message: 'Tên kệ phải có ít nhất 2 ký tự' };
    }

    if (name.length > 100) {
      return { isValid: false, message: 'Tên kệ không được quá 100 ký tự' };
    }

    // Check for special characters that might cause issues
    const invalidChars = /[<>:"\/\\|?*]/;
    if (invalidChars.test(name)) {
      return { isValid: false, message: 'Tên kệ chứa ký tự không hợp lệ' };
    }

    return { isValid: true };
  }

  validateCapacity(capacity: number, currentCount?: number): { isValid: boolean; message?: string } {
    if (!capacity || capacity <= 0) {
      return { isValid: false, message: 'Sức chứa phải lớn hơn 0' };
    }

    if (capacity > 1000) {
      return { isValid: false, message: 'Sức chứa không được quá 1000' };
    }

    if (currentCount && capacity < currentCount) {
      return { 
        isValid: false, 
        message: `Sức chứa không thể nhỏ hơn số sách hiện tại (${currentCount})` 
      };
    }

    return { isValid: true };
  }

  validateLocationCode(locationCode: string): { isValid: boolean; message?: string } {
    if (!locationCode) {
      return { isValid: true }; // Optional field
    }

    if (locationCode.length > 10) {
      return { isValid: false, message: 'Mã vị trí không được quá 10 ký tự' };
    }

    // Location code should follow pattern like A1, B2, etc.
    const locationPattern = /^[A-Z]\d+$/;
    if (!locationPattern.test(locationCode)) {
      return { 
        isValid: false, 
        message: 'Mã vị trí phải theo định dạng A1, B2, C3...' 
      };
    }

    return { isValid: true };
  }

  // Batch validation for multiple fields
  validateMultipleFields(fields: { [key: string]: any }): { [key: string]: { isValid: boolean; message?: string } } {
    const results: { [key: string]: { isValid: boolean; message?: string } } = {};

    Object.keys(fields).forEach(fieldName => {
      const value = fields[fieldName];
      
      switch (fieldName) {
        case 'name':
          results[fieldName] = this.validateShelfName(value);
          break;
        case 'capacity':
          results[fieldName] = this.validateCapacity(value);
          break;
        case 'locationCode':
          results[fieldName] = this.validateLocationCode(value);
          break;
        default:
          results[fieldName] = { isValid: true };
      }
    });

    return results;
  }

  // Sanitize input data
  sanitizeShelfData(data: any): any {
    return {
      ...data,
      name: data.name?.trim(),
      description: data.description?.trim(),
      locationCode: data.locationCode?.trim()?.toUpperCase()
    };
  }
}
