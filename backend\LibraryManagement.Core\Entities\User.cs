using LibraryManagement.Core.Enums;

namespace LibraryManagement.Core.Entities;

public class User : BaseEntity
{
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PasswordHash { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public bool IsActive { get; set; } = true;
    public bool EmailVerified { get; set; } = false;
    public string? EmailVerificationToken { get; set; }
    public DateTime? EmailVerificationTokenExpiry { get; set; }
    public string? PasswordResetToken { get; set; }
    public DateTime? PasswordResetTokenExpiry { get; set; }
    public DateTime? LastLoginDate { get; set; }
    
    // Thêm các trường mới cho xác thực email
    public EmailVerificationMethod? EmailVerificationMethod { get; set; }
    public DateTime? EmailVerifiedAt { get; set; }
    public int? EmailVerifiedById { get; set; }
    public User? EmailVerifiedBy { get; set; }
    
    public string FullName => $"{FirstName} {LastName}";
} 